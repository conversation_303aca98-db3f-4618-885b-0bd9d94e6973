# Business & Review Model Discrepancies Analysis

## Overview
Analysis of discrepancies between Apify API responses and our database schema for businesses and reviews.

## 🏢 Business Model Discrepancies

### Apify Response (`ApifyBusinessInfo`)
```typescript
interface ApifyBusinessInfo {
  placeId: string;           // ✅ Maps to google_place_id
  title: string;             // ✅ Maps to business_name  
  address?: string;          // ✅ Maps to business_address
  phone?: string;            // ✅ Maps to phone_number
  website?: string;          // ✅ Maps to website
  rating?: number;           // ❌ NOT STORED in database
  reviewsCount?: number;     // ❌ NOT STORED in database
  categories?: string[];     // ❌ NOT STORED in database
}
```

### Database Schema (`businesses` table)
```sql
CREATE TABLE businesses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    google_place_id VARCHAR UNIQUE NOT NULL,  -- ✅ From placeId
    business_name VARCHAR NOT NULL,           -- ✅ From title
    business_address TEXT,                    -- ✅ From address
    phone_number VARCHAR,                     -- ✅ From phone
    website VARCHAR,                          -- ✅ From website
    created_at TIMESTAMP WITH TIME ZONE,     -- ❌ Generated, not from Apify
    updated_at TIMESTAMP WITH TIME ZONE      -- ❌ Generated, not from Apify
);
```

### Database Interface (`Business`)
```typescript
interface Business {
  id: string;                 // ❌ Generated UUID, not from Apify
  google_place_id: string;    // ✅ From placeId
  business_name: string;      // ✅ From title
  business_address?: string;  // ✅ From address
  phone_number?: string;      // ✅ From phone
  website?: string;           // ✅ From website
  last_fetch_time?: string;   // ❌ NOT in database schema
  created_at: string;         // ❌ Generated, not from Apify
  updated_at?: string;        // ❌ Generated, not from Apify
}
```

## 📝 Review Model Discrepancies

### Apify Response (`ApifyReview`)
```typescript
interface ApifyReview {
  reviewId: string;        // ✅ Maps to google_review_id
  text: string;           // ✅ Maps to review_text
  rating: number;         // ✅ Maps to rating
  authorName?: string;    // ✅ Maps to author_name
  authorAvatar?: string;  // ✅ Maps to author_avatar
  authorUrl?: string;     // ✅ Maps to author_url
  authorId?: string;      // ✅ Maps to author_id
  publishedAtDate: string; // ✅ Maps to review_date
  updatedAtDate?: string;  // ✅ Maps to updated_review_date
  photos?: string[];       // ✅ Maps to photo_urls (JSONB)
  language?: string;       // ✅ Maps to language
  likes?: number;          // ✅ Maps to likes
  reply?: string;          // ✅ Maps to reply
}
```

### Database Schema (`reviews` table)
```sql
CREATE TABLE reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),      -- ❌ Generated, not from Apify
    google_place_id VARCHAR NOT NULL,            -- ❌ Derived from context, not Apify
    google_review_id VARCHAR UNIQUE NOT NULL,    -- ✅ From reviewId
    review_text TEXT NOT NULL,                   -- ✅ From text
    rating INTEGER CHECK (rating >= 1 AND rating <= 5), -- ✅ From rating
    author_name VARCHAR,                         -- ✅ From authorName
    author_avatar VARCHAR,                       -- ✅ From authorAvatar  
    author_url VARCHAR,                          -- ✅ From authorUrl
    author_id VARCHAR,                           -- ✅ From authorId
    review_date TIMESTAMP WITH TIME ZONE NOT NULL, -- ✅ From publishedAtDate
    updated_review_date TIMESTAMP WITH TIME ZONE,  -- ✅ From updatedAtDate
    photo_urls JSONB DEFAULT '[]'::jsonb,        -- ✅ From photos
    language VARCHAR,                            -- ✅ From language
    original_language VARCHAR,                   -- ❌ Derived, not from Apify
    translated BOOLEAN DEFAULT FALSE,            -- ❌ Generated, not from Apify
    likes INTEGER DEFAULT 0,                     -- ✅ From likes
    reply TEXT,                                  -- ✅ From reply
    sentiment sentiment_type,                    -- ❌ Generated by AI, not from Apify
    created_at TIMESTAMP WITH TIME ZONE,         -- ❌ Generated, not from Apify
    updated_at TIMESTAMP WITH TIME ZONE          -- ❌ Generated, not from Apify
);
```

### Database Interface (`Review`)
```typescript
interface Review {
  id: string;                    // ❌ Generated UUID
  user_id: string;               // ❌ From auth context
  google_place_id: string;       // ❌ From context
  review_text: string;           // ✅ From text
  rating: number;                // ✅ From rating
  author_name?: string;          // ✅ From authorName
  review_date: string;           // ✅ From publishedAtDate
  google_review_id: string;      // ✅ From reviewId
  photo_urls?: string[];         // ✅ From photos
  language?: string;             // ✅ From language
  original_language?: string;    // ❌ Derived
  translated?: boolean;          // ❌ Generated
  sentiment?: SentimentType;     // ❌ Generated by AI
  created_at: string;           // ❌ Generated
}
```

## 🚨 Critical Discrepancies Identified

### Business Model Issues:

1. **❌ Missing Fields in Database**:
   - `rating` (business overall rating from Google)
   - `reviewsCount` (total review count) 
   - `categories` (business categories/types)

2. **❌ Type Inconsistency**:
   - Business interface has `last_fetch_time` but database schema doesn't

3. **❌ Missing Critical Business Data**:
   - Business rating and review count are valuable for analytics but not persisted

### Review Model Issues:

1. **✅ Well Aligned**: Most review fields map correctly from Apify to database

2. **❌ Missing Context Fields**:
   - `user_id` and `google_place_id` are added from context, not Apify
   - This is correct behavior but creates mapping complexity

3. **❌ Derived Fields Mismatch**:
   - `original_language` is set to same as `language` (should be detected separately)
   - `translated` is always `false` initially (should be determined by language detection)

## ⚠️ Potential Issues

### Business Data Loss:
- Google rating and review counts are fetched but immediately discarded
- Business categories/types not stored (useful for competitor analysis)

### Review Processing:
- Language detection logic may be insufficient
- Translation status not properly determined

### Data Consistency:
- Some optional fields use `undefined` in types but database returns `null`

## 🎯 Recommendations

### High Priority:
1. Add business rating, review count, and categories to database schema
2. Fix `last_fetch_time` field inconsistency
3. Improve language detection and translation status logic

### Medium Priority:
1. Standardize null/undefined handling across the stack
2. Add validation for Apify response completeness
3. Consider storing raw Apify response for debugging

### Low Priority:
1. Add metadata fields for better data lineage tracking
2. Consider versioning for schema changes