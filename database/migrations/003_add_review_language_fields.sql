-- Migration to add language tracking fields to reviews table
-- This migration adds support for tracking original language and translation status

-- Step 1: Add language tracking columns to reviews table
ALTER TABLE reviews 
ADD COLUMN language VARCHAR(10),
ADD COLUMN original_language VARCHAR(10),
ADD COLUMN translated BOOLEAN DEFAULT FALSE;

-- Step 2: Create indexes for better performance on language queries
CREATE INDEX IF NOT EXISTS idx_reviews_language ON reviews(language);
CREATE INDEX IF NOT EXISTS idx_reviews_original_language ON reviews(original_language);
CREATE INDEX IF NOT EXISTS idx_reviews_translated ON reviews(translated);

-- Step 3: Add comments to document the new columns
COMMENT ON COLUMN reviews.language IS 'IETF language code of the review text (e.g., "en", "tr", "nl")';
COMMENT ON COLUMN reviews.original_language IS 'IETF language code of the original review if translated (e.g., "tr" for Turkish)';
COMMENT ON COLUMN reviews.translated IS 'Boolean indicating if the review was translated from its original language';

-- Step 4: Update existing reviews to set default values
-- For existing reviews, we'll assume they are in their original language (not translated)
UPDATE reviews 
SET translated = FALSE 
WHERE translated IS NULL;

-- Step 5: Report on the migration
SELECT 
    'Language fields migration completed' as status,
    COUNT(*) as total_reviews,
    COUNT(*) FILTER (WHERE language IS NOT NULL) as reviews_with_language,
    COUNT(*) FILTER (WHERE original_language IS NOT NULL) as reviews_with_original_language,
    COUNT(*) FILTER (WHERE translated = TRUE) as translated_reviews
FROM reviews;
