-- Migration: Add additional fields to reviews table to match Apify data format
-- Date: 2025-08-14
-- Description: Adds author details, photo URLs, language info, likes, and reply fields

-- Add new columns to reviews table
ALTER TABLE reviews 
ADD COLUMN IF NOT EXISTS author_avatar VARCHAR,
ADD COLUMN IF NOT EXISTS author_url VARCHAR,
ADD COLUMN IF NOT EXISTS author_id VARCHAR,
ADD COLUMN IF NOT EXISTS updated_review_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS photo_urls JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS language VARCHAR,
ADD COLUMN IF NOT EXISTS original_language VARCHAR,
ADD COLUMN IF NOT EXISTS translated BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS likes INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS reply TEXT;

-- Create additional indexes for new fields
CREATE INDEX IF NOT EXISTS idx_reviews_language ON reviews(language);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating);
CREATE INDEX IF NOT EXISTS idx_reviews_author_id ON reviews(author_id);
CREATE INDEX IF NOT EXISTS idx_reviews_likes ON reviews(likes);

-- Add comments for documentation
COMMENT ON COLUMN reviews.author_avatar IS 'URL to the review author''s avatar image';
COMMENT ON COLUMN reviews.author_url IS 'URL to the review author''s Google profile';
COMMENT ON COLUMN reviews.author_id IS 'Google ID of the review author';
COMMENT ON COLUMN reviews.updated_review_date IS 'Date when the review was last updated (if different from published date)';
COMMENT ON COLUMN reviews.photo_urls IS 'JSON array of photo objects with id, url, dimensions, location, and caption';
COMMENT ON COLUMN reviews.language IS 'Language code of the review text';
COMMENT ON COLUMN reviews.original_language IS 'Original language if the review was translated';
COMMENT ON COLUMN reviews.translated IS 'Whether the review text has been translated';
COMMENT ON COLUMN reviews.likes IS 'Number of likes/helpful votes the review received';
COMMENT ON COLUMN reviews.reply IS 'Business owner''s reply to the review';

-- Update existing reviews to set default values for new fields
UPDATE reviews 
SET 
  translated = FALSE,
  likes = 0,
  photo_urls = '[]'::jsonb
WHERE 
  translated IS NULL 
  OR likes IS NULL 
  OR photo_urls IS NULL;
