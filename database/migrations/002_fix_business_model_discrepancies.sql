-- Migration to fix critical business model discrepancies
-- File: 002_fix_business_model_discrepancies.sql

-- Add missing fields to businesses table
ALTER TABLE businesses 
ADD COLUMN IF NOT EXISTS last_fetch_time TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS rating DECIMAL(2,1) CHECK (rating >= 0 AND rating <= 5),
ADD COLUMN IF NOT EXISTS total_reviews INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS categories JSONB DEFAULT '[]'::jsonb;

-- Add comments for the new fields
COMMENT ON COLUMN businesses.last_fetch_time IS 'Timestamp of when reviews were last fetched for this business';
COMMENT ON COLUMN businesses.rating IS 'Overall Google rating for the business (0.0-5.0)';
COMMENT ON COLUMN businesses.total_reviews IS 'Total number of reviews for this business on Google';
COMMENT ON COLUMN businesses.categories IS 'Business categories/types from Google (e.g., ["restaurant", "italian_restaurant"])';

-- Create index for rating-based queries
CREATE INDEX IF NOT EXISTS idx_businesses_rating ON businesses(rating);
CREATE INDEX IF NOT EXISTS idx_businesses_last_fetch ON businesses(last_fetch_time);

-- Create index for category-based searches (for competitor analysis)
CREATE INDEX IF NOT EXISTS idx_businesses_categories ON businesses USING GIN(categories);