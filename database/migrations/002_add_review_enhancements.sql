-- Migration to add review management enhancements
-- This migration adds photo URLs support and last fetch time tracking

-- Step 1: Add photo_urls column to reviews table
ALTER TABLE reviews 
ADD COLUMN photo_urls JSONB DEFAULT '[]'::jsonb;

-- Step 2: Add last_fetch_time column to businesses table
ALTER TABLE businesses 
ADD COLUMN last_fetch_time TIMESTAMP WITH TIME ZONE;

-- Step 3: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reviews_photo_urls ON reviews USING GIN (photo_urls);
CREATE INDEX IF NOT EXISTS idx_businesses_last_fetch_time ON businesses(last_fetch_time);

-- Step 4: Add comments to document the new columns
COMMENT ON COLUMN reviews.photo_urls IS 'JSON array of photo URLs from Google Places API reviews';
COMMENT ON COLUMN businesses.last_fetch_time IS 'Timestamp of last review fetch for rate limiting (2 hour minimum)';

-- Step 5: Update the updated_at trigger to include the new columns
-- (The existing trigger will automatically handle updated_at for both tables)

-- Step 6: Report on the migration
SELECT 
    'Migration completed' as status,
    COUNT(*) as total_reviews,
    COUNT(*) FILTER (WHERE photo_urls IS NOT NULL) as reviews_with_photo_support,
    COUNT(*) as total_businesses,
    COUNT(*) FILTER (WHERE last_fetch_time IS NOT NULL) as businesses_with_fetch_time
FROM reviews, businesses;
