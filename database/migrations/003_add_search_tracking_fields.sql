-- Migration to add search tracking fields to businesses table
-- File: 003_add_search_tracking_fields.sql
-- Date: 2025-08-15
-- Description: Adds original_search_query and original_search_url fields for the new scraper service

-- Add missing fields to businesses table
ALTER TABLE businesses 
ADD COLUMN IF NOT EXISTS original_search_query TEXT,
ADD COLUMN IF NOT EXISTS original_search_url TEXT;

-- Add comments for the new fields
COMMENT ON COLUMN businesses.original_search_query IS 'Original search query used to find this business (e.g., "Pizza restaurant in New York")';
COMMENT ON COLUMN businesses.original_search_url IS 'Google Maps URL for this business used for review scraping';

-- Create indexes for search-based queries
CREATE INDEX IF NOT EXISTS idx_businesses_original_search_query ON businesses(original_search_query);
CREATE INDEX IF NOT EXISTS idx_businesses_original_search_url ON businesses(original_search_url);

-- Report on the migration
SELECT 
    'Migration 003 completed' as status,
    COUNT(*) as total_businesses,
    COUNT(*) FILTER (WHERE original_search_query IS NOT NULL) as businesses_with_search_query,
    COUNT(*) FILTER (WHERE original_search_url IS NOT NULL) as businesses_with_search_url
FROM businesses;
