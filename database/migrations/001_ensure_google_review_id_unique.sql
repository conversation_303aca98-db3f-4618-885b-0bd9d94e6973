-- Migration to ensure google_review_id is unique and handle any existing duplicates
-- This migration ensures that google_review_id is globally unique across all reviews

-- First, let's check if there are any duplicate google_review_ids
-- and remove duplicates keeping only the oldest one

-- Step 1: Create a temporary table to identify duplicates
CREATE TEMP TABLE duplicate_reviews AS
SELECT 
    google_review_id,
    MIN(created_at) as first_created,
    COUNT(*) as duplicate_count
FROM reviews 
GROUP BY google_review_id 
HAVING COUNT(*) > 1;

-- Step 2: Delete duplicate reviews, keeping only the first one (oldest)
DELETE FROM reviews 
WHERE id IN (
    SELECT r.id 
    FROM reviews r
    INNER JOIN duplicate_reviews d ON r.google_review_id = d.google_review_id
    WHERE r.created_at > d.first_created
);

-- Step 3: Ensure the unique constraint exists on google_review_id
-- (This should already exist from the schema, but let's make sure)
DO $$ 
BEGIN
    -- Check if the unique constraint already exists
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_constraint 
        WHERE conname = 'reviews_google_review_id_key'
    ) THEN
        -- Add the unique constraint if it doesn't exist
        ALTER TABLE reviews ADD CONSTRAINT reviews_google_review_id_key UNIQUE (google_review_id);
    END IF;
END $$;

-- Step 4: Create an index for better performance on google_review_id lookups
CREATE INDEX IF NOT EXISTS idx_reviews_google_review_id ON reviews(google_review_id);

-- Step 5: Add a comment to document this constraint
COMMENT ON COLUMN reviews.google_review_id IS 'Unique identifier from Google Places API - globally unique across all reviews';

-- Report on the cleanup
SELECT 
    'Cleanup completed' as status,
    COUNT(*) as total_reviews,
    COUNT(DISTINCT google_review_id) as unique_google_review_ids
FROM reviews;
