# ReviewPulse Monorepo - Global .gitignore

# ============================================
# ENVIRONMENT & SECRETS
# ============================================
.env
.env.*
!.env.example
*.pem
secrets/

# ============================================
# NODE.JS & FRONTEND (Next.js)
# ============================================
# Dependencies
node_modules/
**/node_modules/
.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Build outputs
.next/
out/
build/
dist/

# Testing
coverage/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Vercel deployment
.vercel

# ============================================
# IDE & EDITOR
# ============================================
.vscode/
.idea/
*.swp
*.swo
*~

# ============================================
# OS GENERATED
# ============================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ============================================
# LOGS & MONITORING
# ============================================
*.log
logs/
*.log.*

# ============================================
# TEMPORARY & CACHE
# ============================================
tmp/
temp/
.cache/
