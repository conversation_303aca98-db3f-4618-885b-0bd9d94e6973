name: Generate Supabase Types

on:
  # Run on database schema changes
  push:
    paths:
      - 'database/schema.sql'
      - 'database/migrations/**'
  
  # Run manually
  workflow_dispatch:
  
  # Run on schedule (daily at 2 AM UTC)
  schedule:
    - cron: '0 2 * * *'

jobs:
  generate-types:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: Install dependencies
        working-directory: frontend
        run: npm ci
        
      - name: Install Supabase CLI
        run: npm install -g supabase
        
      - name: Generate types
        working-directory: frontend
        run: npm run types:generate
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          
      - name: Check for changes
        id: verify-changed-files
        run: |
          if [ -n "$(git status --porcelain frontend/src/types/supabase.ts)" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Commit and push changes
        if: steps.verify-changed-files.outputs.changed == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add frontend/src/types/supabase.ts
          git commit -m "chore: update Supabase types [skip ci]"
          git push
          
      - name: Create Pull Request
        if: steps.verify-changed-files.outputs.changed == 'true' && github.ref != 'refs/heads/main'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore: update Supabase types"
          title: "🤖 Update Supabase Types"
          body: |
            This PR updates the Supabase types based on the latest database schema.
            
            ## Changes
            - Updated `src/types/supabase.ts` with latest database schema
            
            ## Generated by
            - GitHub Actions workflow
            - Triggered by: ${{ github.event_name }}
            
            ## Review
            Please review the type changes to ensure they match the expected database schema.
          branch: update-supabase-types
          delete-branch: true
