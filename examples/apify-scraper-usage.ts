/**
 * Example usage of the simplified ApifyScraper service
 * 
 * This demonstrates how to use the two main functions:
 * 1. scrapeBusiness - Scrape business information and save to database
 * 2. scrapeReviewsForBusiness - Scrape reviews for multiple businesses using database IDs
 */

import { ApifyScraper } from '@/lib/services/apify';

// Initialize the scraper with your Apify API token
const scraper = new ApifyScraper(process.env.APIFY_API_TOKEN!);

/**
 * Example 1: Scrape a single business
 */
async function exampleScrapeBusiness() {
  try {
    // Scrape business information and save to database
    const businessId = await scraper.scrapeBusiness(
      "Pizza restaurant in New York", // Search query
      "user-123" // Optional: User ID to create business profile
    );
    
    console.log('✅ Business scraped and saved with ID:', businessId);
  } catch (error) {
    console.error('❌ Error scraping business:', error);
  }
}

/**
 * Example 2: Scrape reviews for multiple businesses
 * NOTE: Now uses database business IDs instead of Google Place IDs
 */
async function exampleScrapeReviews() {
  try {
    // Array of database business IDs (returned from scrapeBusiness)
    const businessIds = [
      "123e4567-e89b-12d3-a456-426614174000", // Business ID 1
      "987fcdeb-51a2-43d7-8f9e-123456789abc", // Business ID 2
      "456789ab-cdef-1234-5678-9abcdef01234"  // Business ID 3
    ];
    
    // Scrape reviews for all businesses (max 50 reviews per business)
    // The scraper will:
    // 1. Query database for each business ID
    // 2. Get the original_search_url for each business
    // 3. Use that URL to scrape reviews from Apify
    // 4. Save reviews to database
    await scraper.scrapeReviewsForBusiness(businessIds, 50);
    
    console.log('✅ Reviews scraped for all businesses');
  } catch (error) {
    console.error('❌ Error scraping reviews:', error);
  }
}

/**
 * Example 3: Complete workflow - scrape business then its reviews
 */
async function exampleFullScrapeWorkflow() {
  try {
    // Step 1: Scrape business information
    const businessId = await scraper.scrapeBusiness("Coffee shop in San Francisco");
    console.log('✅ Business scraped:', businessId);
    
    // Step 2: Scrape reviews for the business using its database ID
    await scraper.scrapeReviewsForBusiness([businessId], 100);
    console.log('✅ Reviews scraped for business');
    
  } catch (error) {
    console.error('❌ Error in full scrape workflow:', error);
  }
}

/**
 * Example 4: Batch processing multiple business queries
 */
async function exampleBatchScraping() {
  const queries = [
    "Italian restaurant in Boston",
    "Hair salon in Miami", 
    "Auto repair shop in Chicago",
    "Dentist in Seattle"
  ];
  
  const businessIds: string[] = [];
  
  // Step 1: Scrape all businesses
  for (const query of queries) {
    try {
      const businessId = await scraper.scrapeBusiness(query);
      businessIds.push(businessId);
      console.log(`✅ Scraped business for query: ${query} (ID: ${businessId})`);
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`❌ Error scraping business for query "${query}":`, error);
    }
  }
  
  // Step 2: Scrape reviews for all businesses at once
  if (businessIds.length > 0) {
    try {
      await scraper.scrapeReviewsForBusiness(businessIds, 25);
      console.log(`✅ Reviews scraped for ${businessIds.length} businesses`);
    } catch (error) {
      console.error('❌ Error scraping reviews for batch:', error);
    }
  }
  
  console.log(`✅ Batch scraping completed. ${businessIds.length} businesses processed.`);
}

/**
 * Example 5: Working with database service to verify results
 */
async function exampleVerifyResults() {
  try {
    // Import database service
    const { DatabaseService } = await import('@/lib/services/database');
    const database = new DatabaseService(true);
    
    // Scrape a business
    const businessId = await scraper.scrapeBusiness("Shake Shack Madison Square Park");
    
    // Get business details from database
    const business = await database.getBusinessById(businessId);
    if (business) {
      console.log('📊 Business Details:');
      console.log(`   Name: ${business.business_name}`);
      console.log(`   Place ID: ${business.google_place_id}`);
      console.log(`   Address: ${business.business_address}`);
      console.log(`   Rating: ${business.rating}`);
      console.log(`   Total Reviews: ${business.total_reviews}`);
      console.log(`   Original Search URL: ${business.original_search_url}`);
      
      // Scrape reviews
      await scraper.scrapeReviewsForBusiness([businessId], 10);
      
      // Get reviews from database
      const reviews = await database.getRecentReviews(business.google_place_id, 20);
      console.log(`📝 Found ${reviews.length} reviews in database`);
      
      if (reviews.length > 0) {
        const sampleReview = reviews[0];
        if (sampleReview) {
          console.log('📄 Sample Review:');
          console.log(`   Author: ${sampleReview.author_name || 'Anonymous'}`);
          console.log(`   Rating: ${sampleReview.rating || 0}/5`);
          console.log(`   Text: ${(sampleReview.review_text || '').substring(0, 100)}...`);
          console.log(`   Date: ${sampleReview.review_date || 'Unknown'}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error verifying results:', error);
  }
}

/**
 * Example 6: Error handling and edge cases
 */
async function exampleErrorHandling() {
  try {
    // Test with invalid business ID
    console.log('🧪 Testing error handling...');
    
    try {
      await scraper.scrapeReviewsForBusiness(['invalid-business-id'], 5);
    } catch {
      console.log('✅ Invalid business ID properly handled');
    }
    
    // Test with empty query
    try {
      await scraper.scrapeBusiness("");
    } catch {
      console.log('✅ Empty query properly handled');
    }
    
    // Test with very specific query that might not exist
    try {
      const businessId = await scraper.scrapeBusiness("XYZ123NonExistentBusiness456");
      console.log('⚠️  Unexpected business found for non-existent query:', businessId);
    } catch {
      console.log('✅ Non-existent business query properly handled');
    }
    
  } catch (error) {
    console.error('❌ Error in error handling test:', error);
  }
}

// Export examples for use in other files
export {
  exampleScrapeBusiness,
  exampleScrapeReviews,
  exampleFullScrapeWorkflow,
  exampleBatchScraping,
  exampleVerifyResults,
  exampleErrorHandling
};

// If running this file directly
if (require.main === module) {
  // Run examples
  console.log('🚀 Running ApifyScraper examples...');
  
  // Uncomment the example you want to run:
  // exampleScrapeBusiness();
  // exampleScrapeReviews();
  // exampleFullScrapeWorkflow();
  // exampleBatchScraping();
  // exampleVerifyResults();
  // exampleErrorHandling();
  
  console.log('💡 Uncomment an example function above to run it');
}
