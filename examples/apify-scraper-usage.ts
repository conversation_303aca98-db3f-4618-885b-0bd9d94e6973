/**
 * Example usage of the simplified ApifyScraper service
 * 
 * This demonstrates how to use the two main functions:
 * 1. scrapeBusiness - Scrape business information and save to database
 * 2. scrapeReviewsForBusiness - Scrape reviews for multiple businesses
 */

import { ApifyScraper } from '@/lib/services/apify';

// Initialize the scraper with your Apify API token
const scraper = new ApifyScraper(process.env.APIFY_API_TOKEN!);

/**
 * Example 1: Scrape a single business
 */
async function exampleScrapeBusiness() {
  try {
    // Scrape business information and save to database
    const businessId = await scraper.scrapeBusiness(
      "Pizza restaurant in New York", // Search query
      "user-123" // Optional: User ID to create business profile
    );
    
    console.log('✅ Business scraped and saved with ID:', businessId);
  } catch (error) {
    console.error('❌ Error scraping business:', error);
  }
}

/**
 * Example 2: Scrape reviews for multiple businesses
 */
async function exampleScrapeReviews() {
  try {
    // Array of Google Place IDs
    const placeIds = [
      "ChIJN1t_tDeuEmsRUsoyG83frY4", // Example Place ID 1
      "ChIJrTLr-GyuEmsRBfy61i59si0", // Example Place ID 2
      "ChIJN5X_gDeuEmsRxB9z1FzSrpY"  // Example Place ID 3
    ];
    
    // Scrape reviews for all businesses (max 50 reviews per business)
    await scraper.scrapeReviewsForBusiness(placeIds, 50);
    
    console.log('✅ Reviews scraped for all businesses');
  } catch (error) {
    console.error('❌ Error scraping reviews:', error);
  }
}

/**
 * Example 3: Background job to scrape business and then its reviews
 */
async function exampleFullScrapeWorkflow() {
  try {
    // Step 1: Scrape business information
    const businessId = await scraper.scrapeBusiness("Coffee shop in San Francisco");
    console.log('✅ Business scraped:', businessId);
    
    // Step 2: Get the business to extract place ID
    // Note: You would need to implement a method to get business by ID
    // or modify scrapeBusiness to return the place ID as well
    
    // For this example, let's assume we have the place ID
    const placeId = "ChIJExample123"; // This would come from the business record
    
    // Step 3: Scrape reviews for the business
    await scraper.scrapeReviewsForBusiness([placeId], 100);
    console.log('✅ Reviews scraped for business');
    
  } catch (error) {
    console.error('❌ Error in full scrape workflow:', error);
  }
}

/**
 * Example 4: Batch processing multiple business queries
 */
async function exampleBatchScraping() {
  const queries = [
    "Italian restaurant in Boston",
    "Hair salon in Miami", 
    "Auto repair shop in Chicago",
    "Dentist in Seattle"
  ];
  
  const businessIds: string[] = [];
  
  // Scrape all businesses
  for (const query of queries) {
    try {
      const businessId = await scraper.scrapeBusiness(query);
      businessIds.push(businessId);
      console.log(`✅ Scraped business for query: ${query}`);
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`❌ Error scraping business for query "${query}":`, error);
    }
  }
  
  console.log(`✅ Batch scraping completed. ${businessIds.length} businesses scraped.`);
}

// Export examples for use in other files
export {
  exampleScrapeBusiness,
  exampleScrapeReviews,
  exampleFullScrapeWorkflow,
  exampleBatchScraping
};

// If running this file directly
if (require.main === module) {
  // Run examples
  console.log('🚀 Running ApifyScraper examples...');
  
  // Uncomment the example you want to run:
  // exampleScrapeBusiness();
  // exampleScrapeReviews();
  // exampleFullScrapeWorkflow();
  // exampleBatchScraping();
}
