# Development Guide

## Overview

This is a full-stack Next.js application for business review management and sentiment analysis. The application uses Supabase for authentication and database, Apify for data scraping, and various AI services for sentiment analysis and summarization.

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API routes, Supabase, Apify
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **AI Services**: OpenAI, Google Gemini, Anthropic Claude, Azure Text Analytics
- **State Management**: TanStack Query (React Query)
- **Validation**: Zod
- **Internationalization**: next-intl

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # Backend API routes
│   ├── dashboard/         # Protected dashboard pages
│   ├── login/            # Authentication pages
│   ├── onboarding/       # User onboarding flow
│   └── register/         # User registration
├── components/           # React components
│   ├── ui/              # Reusable UI components (shadcn/ui)
│   └── *.tsx            # Feature-specific components
├── hooks/               # Custom React hooks
├── lib/                 # Utility libraries and services
│   ├── services/        # Backend service classes
│   ├── api.ts          # Frontend API client
│   ├── auth-server.ts  # Server-side authentication
│   └── validations.ts  # Zod schemas
├── types/              # TypeScript type definitions
│   ├── api/           # API-specific types
│   ├── index.ts       # General types
│   └── supabase.ts    # Generated Supabase types
├── contexts/          # React contexts
├── providers/         # React providers
└── i18n/             # Internationalization
```

## UI Components

### Core UI Components (shadcn/ui)
- **Alert** - Display important messages and notifications
- **Badge** - Small status indicators and labels
- **Button** - Primary action buttons with variants
- **Card** - Container component for content sections
- **Checkbox** - Boolean input controls
- **Dialog** - Modal dialogs and overlays
- **Dropdown Menu** - Contextual menu components
- **Form** - Form wrapper with validation support
- **Input** - Text input fields with validation
- **Label** - Form field labels
- **Progress** - Progress bars and loading indicators
- **Radio Group** - Single-choice selection controls
- **Select** - Dropdown selection components
- **Sheet** - Slide-out panels and drawers
- **Slider** - Range input controls
- **Switch** - Toggle switches
- **Tabs** - Tabbed navigation components
- **Textarea** - Multi-line text input
- **Typography** - Text styling components (H1, H2, Text, etc.)

### Feature Components
- **BusinessConfirmationDialog** - Confirm business details before saving
- **BusinessHeader** - Display business information and stats
- **GlobalHeader** - Main navigation header with auth controls
- **GoogleMapsUrlInput** - Input for Google Maps business URLs
- **OnboardingFlow** - Multi-step user onboarding process
- **Pagination** - Navigate through paginated data
- **ProtectedRoute** - Route protection wrapper
- **ReviewCard** - Display individual review information
- **SavedBusinessesList** - List of user's saved businesses
- **SentimentChart** - Visualize review sentiment data

### Form Components
- **FormWrapper** - Form container with validation
- **FormField** - Individual form field with error handling

## Backend Endpoints

### Authentication
- **GET /api/user/profile** - Get current user profile information
- **POST /api/user/profile** - Create or update user profile

### Business Management
- **POST /api/businesses/search-from-url** - Search business info from Google Maps URL
- **POST /api/businesses/save** - Save business to user's account
- **GET /api/businesses/my-businesses** - Get user's saved businesses
- **DELETE /api/businesses/my-businesses/[id]** - Remove business from user's account

### Reviews Management
- **POST /api/reviews/fetch** - Fetch reviews from external sources (Apify)
- **GET /api/reviews/paginated** - Get paginated reviews with filtering
- **GET /api/reviews/stats** - Get review statistics and analytics

### Summaries & Analytics
- **POST /api/summaries/generate** - Generate AI-powered review summaries
- **GET /api/summaries** - Get user's saved summaries

### Competitors (Future)
- **GET /api/competitors/list** - List competitor businesses
- **POST /api/competitors/setup** - Set up competitor tracking

## Development Workflow

### 1. Environment Setup
```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Fill in required API keys and database URLs

# Generate types from Supabase
npm run types:generate

# Start development server
npm run dev
```

### 2. Development Commands
```bash
# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix

# Formatting
npm run format
npm run format:check

# Run all checks
npm run check-all

# Generate types
npm run types:generate

# Migrate example data
npm run migrate:example-data

# Complete demo setup
npm run setup:demo
```

### 3. Adding New Features

#### Frontend Components
1. Create component in `src/components/`
2. Use existing UI components from `src/components/ui/`
3. Follow naming convention: PascalCase for components
4. Add TypeScript interfaces for props
5. Update this documentation

#### Backend Endpoints
1. Create route in `src/app/api/[domain]/[action]/route.ts`
2. Use Zod for request validation
3. Implement proper error handling
4. Add authentication using `authenticateUser` from `@/lib/auth-server`
5. Follow RESTful conventions
6. Update this documentation

#### Custom Hooks
1. Create hook in `src/hooks/`
2. Use TanStack Query for data fetching
3. Follow naming convention: `use[FeatureName]`
4. Include proper TypeScript types
5. Handle loading and error states

#### Types
1. Add API types to `src/types/api/[domain].ts`
2. Export from `src/types/api/index.ts`
3. Use `import type` for type-only imports
4. Run `npm run types:generate` after database changes

### 4. Common Patterns

#### API Client Usage
```typescript
import { apiClient } from '@/lib/api';
import type { BusinessInfo } from '@/types/api';

// In components/hooks
const businesses = await apiClient.getMyBusinesses();
```

#### Authentication Pattern
```typescript
// Server-side (API routes)
import { authenticateUser } from '@/lib/auth-server';

export async function GET(request: NextRequest) {
  const { user } = await authenticateUser(request);
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  // ... rest of endpoint
}
```

#### Form Validation
```typescript
import { z } from 'zod';

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email'),
});

type FormData = z.infer<typeof schema>;
```

#### Custom Hook Pattern
```typescript
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';

export function useBusinesses() {
  const queryClient = useQueryClient();
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['businesses'],
    queryFn: apiClient.getMyBusinesses,
  });
  
  const mutation = useMutation({
    mutationFn: apiClient.saveBusiness,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['businesses'] });
    },
  });
  
  return { data, isLoading, error, saveBusiness: mutation.mutateAsync };
}
```

## Development Tips

1. **Always use the consolidated `apiClient`** - Never import individual API clients
2. **Use TypeScript strictly** - Import types from `@/types/api`
3. **Follow the established patterns** - Look at existing components/endpoints for reference
4. **Test authentication** - Use the working auth pattern from `auth-server.ts`
5. **Update types after changes** - Run `npm run types:generate` after database schema changes
6. **Use TanStack Query** - For all data fetching and state management
7. **Validate inputs** - Use Zod schemas for all API endpoints
8. **Handle errors gracefully** - Provide meaningful error messages
9. **Use internationalization** - Support multiple languages with next-intl
10. **Follow UI consistency** - Use existing shadcn/ui components

## Testing

The final test of every change is conducted by the user. Before submitting:

1. Run `npm run check-all` to verify code quality
2. Test the feature manually in the browser
3. Verify authentication works correctly
4. Check that types are properly generated
5. Ensure no console errors or warnings

## Service Layer Architecture

### Backend Services
- **ApifyService** - Interface with Apify API for business and review data scraping
- **BusinessService** - Business management operations and Google Maps URL parsing
- **DatabaseService** - Supabase database operations with service role authentication
- **ReviewService** - Review fetching, processing, and sentiment analysis coordination
- **SentimentAnalysisService** - AI-powered sentiment analysis using multiple providers
- **SummarizationService** - AI-powered review summarization and insights generation

### Service Pattern
```typescript
export class ExampleService {
  private databaseService: DatabaseService;

  constructor(apiToken?: string) {
    this.databaseService = new DatabaseService(true); // Use service role
  }

  async performOperation(data: InputType, user: AuthUser): Promise<OutputType> {
    // Validate input
    // Process data
    // Save to database
    // Return result
  }
}
```

## Data Flow

1. **User Action** → Component
2. **Component** → Custom Hook (TanStack Query)
3. **Hook** → API Client
4. **API Client** → Next.js API Route
5. **API Route** → Service Layer
6. **Service** → External APIs / Database
7. **Response** ← Back through the chain

## Environment Variables

Required environment variables:
```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Apify
APIFY_API_TOKEN=

# AI Services (choose one or more)
OPENAI_API_KEY=
GOOGLE_GENERATIVE_AI_API_KEY=
ANTHROPIC_API_KEY=
AZURE_TEXT_ANALYTICS_ENDPOINT=
AZURE_TEXT_ANALYTICS_KEY=

# Rate Limiting
RATE_LIMIT_SECONDS=7200
```

## Database Schema

Key tables:
- **users** - User authentication (managed by Supabase Auth)
- **user_profiles** - Extended user information
- **businesses** - Business information from Google Places
- **business_profiles** - User-business relationships
- **reviews** - Review data with sentiment analysis
- **summaries** - AI-generated review summaries

## Internationalization

Supported languages: English (en), Spanish (es), Dutch (nl), Turkish (tr), German (de)

### Adding New Translations
1. Add messages to `src/i18n/messages/[locale].json`
2. Use translation hooks in components:
```typescript
import { useTranslations } from 'next-intl';

function Component() {
  const t = useTranslations('ComponentName');
  return <div>{t('messageKey')}</div>;
}
```

## Recent Changes

- **Type Organization**: Moved all TypeScript interfaces to `src/types/api/` folder structure
- **Authentication Separation**: Split server-side auth (`auth-server.ts`) from client-side (`useAuth.ts`)
- **API Consolidation**: Unified all API calls through single `apiClient` instance
- **Centralized Types**: Created barrel exports in `src/types/api/index.ts`
- **Service Layer**: Implemented comprehensive service classes for backend operations
- **Rate Limiting**: Added configurable rate limiting for API operations
