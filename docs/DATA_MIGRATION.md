# Data Migration Guide

This document explains how to migrate example data into your Supabase database and generate corresponding TypeScript types.

## Overview

The migration system allows you to:
1. **Populate the database** with realistic example data from Apify
2. **Generate TypeScript types** that match your database schema
3. **Create test users** for development and testing
4. **Set up demo data** for showcasing the application

## Quick Start

### Complete Demo Setup

```bash
# Run complete demo setup (migration + type generation)
npm run setup:demo
```

### Individual Commands

```bash
# Migrate example data only
npm run migrate:example-data

# Generate types only
npm run types:generate
```

## Migration Script Details

### What Gets Migrated

The `scripts/migrate-example-data.js` script migrates:

1. **Test User**
   - Email: `<EMAIL>`
   - Password: `testpassword123`
   - Role: `user`

2. **Businesses** (50 restaurants from Apify data)
   - Business names, addresses, phone numbers
   - Google Place IDs extracted from URLs
   - Website information where available

3. **Reviews** (Sample reviews from Apify data)
   - Review text, ratings, author names
   - Publication dates and sentiment analysis
   - Randomly distributed across businesses

4. **Business Profiles** (10 profiles)
   - Links test user to first 10 businesses
   - Simulates user's saved businesses

5. **Sample Summary**
   - Monthly summary with themes and insights
   - Sentiment distribution data
   - Recommended improvements

### Data Sources

The migration uses example data from:
- `src/data/fetchBusinessesFromApify.json` - Business information
- `src/data/fetchReviewsFromApify.json` - Review data

### Migration Process

```javascript
// 1. Create test user
const userId = await createTestUser();

// 2. Migrate businesses
const businesses = await migrateBusinesses();

// 3. Migrate reviews
const reviewsCount = await migrateReviews(userId, businesses);

// 4. Create business profiles
const profilesCount = await createBusinessProfiles(userId, businesses);

// 5. Generate sample summary
await generateSampleSummary(userId);
```

## Environment Requirements

The migration script requires these environment variables:

```env
# Required for database access
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

These are automatically loaded from `.env.local`.

## Generated Data Structure

### Businesses Table
```sql
INSERT INTO businesses (
  google_place_id,
  business_name,
  business_address,
  phone_number,
  website
) VALUES (...);
```

### Reviews Table
```sql
INSERT INTO reviews (
  user_id,
  google_place_id,
  google_review_id,
  review_text,
  rating,
  author_name,
  review_date,
  sentiment
) VALUES (...);
```

### Business Profiles Table
```sql
INSERT INTO business_profiles (
  user_id,
  business_id
) VALUES (...);
```

## Type Generation

After migration, types are automatically generated from the database schema:

### Generated Types

```typescript
// Core table types
export type Business = Tables<'businesses'>
export type Review = Tables<'reviews'>
export type BusinessProfile = Tables<'business_profiles'>
export type Summary = Tables<'summaries'>

// Insert types for creating new records
export type BusinessInsert = TablesInsert<'businesses'>
export type ReviewInsert = TablesInsert<'reviews'>

// Update types for modifying records
export type BusinessUpdate = TablesUpdate<'businesses'>
export type ReviewUpdate = TablesUpdate<'reviews'>

// Extended types with relationships
export type BusinessProfileWithBusiness = BusinessProfile & {
  businesses: Business
}
```

### Usage in Code

```typescript
import { Business, ReviewInsert, Database } from '@/types/supabase'
import { createClient } from '@supabase/supabase-js'

// Typed Supabase client
const supabase = createClient<Database>(url, key)

// Type-safe queries
const { data }: { data: Business[] | null } = await supabase
  .from('businesses')
  .select('*')

// Type-safe inserts
const newReview: ReviewInsert = {
  user_id: 'user-123',
  google_place_id: 'place-456',
  review_text: 'Great service!',
  rating: 5,
  author_name: 'John Doe',
  review_date: new Date().toISOString(),
  google_review_id: 'review-789'
}
```

## Testing the Migration

### 1. Verify Database Content

```sql
-- Check migrated businesses
SELECT COUNT(*) FROM businesses;

-- Check migrated reviews
SELECT COUNT(*) FROM reviews;

-- Check business profiles
SELECT COUNT(*) FROM business_profiles;

-- Check test user
SELECT email FROM auth.users WHERE email = '<EMAIL>';
```

### 2. Test Application Login

1. Start the development server: `npm run dev`
2. Navigate to the login page
3. Login with:
   - Email: `<EMAIL>`
   - Password: `testpassword123`

### 3. Verify Type Safety

```bash
# Run TypeScript checks
npm run type-check

# Should pass without errors
```

## Troubleshooting

### Common Issues

#### 1. Environment Variables Missing

```
❌ Missing required environment variables:
   NEXT_PUBLIC_SUPABASE_URL
   SUPABASE_SERVICE_ROLE_KEY
```

**Solution:** Ensure `.env.local` exists with correct values.

#### 2. User Already Exists

```
✓ Test user already exists
```

This is normal - the script handles existing users gracefully.

#### 3. Duplicate Data

The script automatically skips existing records based on unique constraints:
- Businesses: `google_place_id`
- Reviews: `google_review_id`
- Business Profiles: `(user_id, business_id)`

#### 4. Type Generation Fails

If type generation fails, check:
1. Supabase CLI authentication: `supabase login`
2. Project access permissions
3. Network connectivity

### Manual Cleanup

To reset the migrated data:

```sql
-- Delete in order to respect foreign key constraints
DELETE FROM business_profiles WHERE user_id IN (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);

DELETE FROM reviews WHERE user_id IN (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);

DELETE FROM summaries WHERE user_id IN (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);

DELETE FROM businesses WHERE google_place_id LIKE '%';

-- Delete test user (use Supabase dashboard or auth admin API)
```

## Best Practices

### 1. Development Workflow

```bash
# 1. Set up demo data
npm run setup:demo

# 2. Start development
npm run dev

# 3. Test with migrated data
# <NAME_EMAIL>

# 4. Make schema changes
# Edit database/schema.sql

# 5. Regenerate types
npm run types:generate
```

### 2. Team Collaboration

- **Commit generated types** to version control
- **Document schema changes** in pull requests
- **Run migration** on new developer setups
- **Keep example data updated** with real-world scenarios

### 3. Production Considerations

- **Never run migration scripts** in production
- **Use separate test databases** for development
- **Backup data** before schema changes
- **Test migrations** in staging environments

## Related Files

- `scripts/migrate-example-data.js` - Main migration script
- `src/data/fetchBusinessesFromApify.json` - Business example data
- `src/data/fetchReviewsFromApify.json` - Review example data
- `database/schema.sql` - Database schema
- `src/types/supabase.ts` - Generated TypeScript types
