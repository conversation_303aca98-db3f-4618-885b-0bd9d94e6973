# Supabase Type Generation

This document explains how to generate and maintain TypeScript types from your Supabase database schema.

## Overview

We use Supabase CLI to automatically generate TypeScript types that match our database schema. This ensures type safety across the entire application and prevents runtime errors due to schema mismatches.

## Quick Start

### Generate Types

```bash
# Generate types from remote Supabase project
npm run types:generate

# Alternative: Generate directly with Supabase CLI
npm run types:generate-remote

# For local development (requires local Supabase setup)
npm run types:generate-local
```

### Development Setup

```bash
# Run the complete development setup (includes type generation)
./scripts/setup-dev.sh
```

## Available Scripts

| Script | Description |
|--------|-------------|
| `npm run types:generate` | Smart type generation with fallbacks |
| `npm run types:generate-remote` | Generate from remote Supabase project |
| `npm run types:generate-local` | Generate from local Supabase instance |
| `npm run types:watch` | Generate types and show success message |

## How It Works

### 1. Automatic Generation

The `scripts/generate-types.js` script:

1. **Checks Supabase CLI installation**
2. **Attempts remote generation** using project ID
3. **Falls back to schema-based generation** if remote fails
4. **Adds helper types** for easier usage
5. **Provides detailed logging** of the process

### 2. Type Structure

Generated types include:

```typescript
// Core database types
export type Database = {
  public: {
    Tables: { /* ... */ }
    Views: { /* ... */ }
    Functions: { /* ... */ }
    Enums: { /* ... */ }
  }
}

// Helper types for easier usage
export type Tables<T> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T> = Database['public']['Tables'][T]['Update']

// Specific table types
export type Business = Tables<'businesses'>
export type Review = Tables<'reviews'>
// ... etc
```

### 3. Usage in Code

```typescript
import { Business, ReviewInsert, Database } from '@/types/supabase'
import { createClient } from '@supabase/supabase-js'

// Typed Supabase client
const supabase = createClient<Database>(url, key)

// Type-safe operations
const { data }: { data: Business[] | null } = await supabase
  .from('businesses')
  .select('*')

// Type-safe inserts
const newReview: ReviewInsert = {
  user_id: 'user-123',
  google_place_id: 'place-456',
  review_text: 'Great service!',
  rating: 5,
  // TypeScript will enforce all required fields
}
```

## Authentication

### Supabase CLI Authentication

For remote type generation, you need to authenticate with Supabase:

```bash
# Login to Supabase (opens browser)
supabase login

# Verify authentication
supabase projects list
```

### Environment Variables

The script uses these environment variables:

```env
# For remote generation (optional, uses CLI auth if not provided)
SUPABASE_ACCESS_TOKEN=your_access_token

# For local generation
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Automation

### GitHub Actions

The `.github/workflows/generate-types.yml` workflow automatically:

1. **Runs on schema changes** (when `database/schema.sql` is modified)
2. **Runs daily** to catch any manual database changes
3. **Can be triggered manually** from GitHub Actions tab
4. **Creates pull requests** with type updates

### Post-install Hook

Types are automatically generated after `npm install` via the `postinstall` script.

## Troubleshooting

### Common Issues

#### 1. Authentication Errors

```bash
Error: Failed to generate types from remote project
```

**Solution:**
```bash
supabase login
# or set SUPABASE_ACCESS_TOKEN environment variable
```

#### 2. CLI Not Found

```bash
Error: Supabase CLI is not installed
```

**Solution:**
```bash
npm install -g supabase
```

#### 3. Network Issues

If remote generation fails, the script automatically falls back to schema-based generation.

#### 4. Type Mismatches

If you see TypeScript errors after schema changes:

1. Regenerate types: `npm run types:generate`
2. Check for breaking changes in the database schema
3. Update your code to match the new types

### Manual Verification

```bash
# Check generated types
cat src/types/supabase.ts

# Verify TypeScript compilation
npm run type-check

# Run all checks
npm run check-all
```

## Best Practices

### 1. Regular Updates

- Run `npm run types:generate` after database schema changes
- Set up the GitHub Action for automatic updates
- Include type generation in your CI/CD pipeline

### 2. Version Control

- **Commit generated types** to version control
- **Review type changes** in pull requests
- **Tag releases** when making breaking schema changes

### 3. Development Workflow

```bash
# 1. Make database schema changes
# 2. Generate new types
npm run types:generate

# 3. Update code to match new types
# 4. Run checks
npm run check-all

# 5. Commit changes
git add src/types/supabase.ts
git commit -m "feat: update types for new schema"
```

### 4. Team Collaboration

- **Document schema changes** in pull requests
- **Communicate breaking changes** to the team
- **Use semantic versioning** for schema changes

## Advanced Usage

### Custom Type Extensions

You can extend the generated types:

```typescript
// src/types/custom.ts
import { Tables } from './supabase'

export type BusinessWithReviews = Tables<'businesses'> & {
  reviews: Tables<'reviews'>[]
}

export type ReviewWithSentiment = Tables<'reviews'> & {
  sentiment_score: number
}
```

### Type Guards

Create type guards for runtime validation:

```typescript
import { Business } from '@/types/supabase'

export function isBusiness(obj: any): obj is Business {
  return obj && 
    typeof obj.id === 'string' &&
    typeof obj.business_name === 'string' &&
    typeof obj.google_place_id === 'string'
}
```

## Support

For issues with type generation:

1. Check this documentation
2. Verify Supabase CLI installation and authentication
3. Check the GitHub Actions logs for automated generation
4. Create an issue with the error message and steps to reproduce

## Related Files

- `scripts/generate-types.js` - Main type generation script
- `scripts/setup-dev.sh` - Development setup script
- `.github/workflows/generate-types.yml` - GitHub Actions workflow
- `database/schema.sql` - Database schema source
- `src/types/supabase.ts` - Generated types (auto-generated, do not edit manually)
