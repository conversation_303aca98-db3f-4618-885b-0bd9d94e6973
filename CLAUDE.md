# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**ReviewPulse** is a SaaS MVP for SMEs that aggregates Google Reviews, applies AI-powered sentiment analysis, and generates actionable weekly/monthly summaries. The system focuses on centralized customer feedback analysis with automated email reporting and a web dashboard.

## 🎯 Development Guidelines for AI Agents

### Frontend Development (CRITICAL REQUIREMENTS)
- **API Integration**: MUST use TanStack Query, NEVER regular fetch
- **Components**: ALWAYS use shadcn/ui design system components, NEVER create custom UI components
- **Forms**: ALWAYS use React Hook Form + Zod + FormWrapper pattern
- **Internationalization**: NEVER hardcode strings, ALWAYS use i18n with EN/TR/NL translations
- **Code Quality**: Run `npm run check-all` before any changes (TypeScript + ESLint + Prettier)
- **Error Handling**: Implement loading and error states gracefully for all operations
- **Styling**: Use Tailwind CSS exclusively, no custom CSS files

### Backend Development (CRITICAL REQUIREMENTS)
- **Authentication**: ALWAYS validate JWT tokens using Supabase auth
- **Data Isolation**: ALWAYS filter by user_id for user-specific data
- **Service Layer**: Keep business logic in services, routes should be thin handlers
- **Error Handling**: Use proper HTTP status codes (400/401/404/500) with detailed logging
- **External APIs**: Implement rate limiting, timeouts, and fallback mechanisms
- **Validation**: Use Pydantic models for all request/response data
- **Async Operations**: Use async/await for all I/O operations

See `docs/FRONTEND_DEVELOPMENT_GUIDE.md` and `docs/BACKEND_DEVELOPMENT_GUIDE.md` for comprehensive guidelines.

## Tech Stack

| Layer | Technology | Version |
|-------|------------|---------|
| **Frontend** | Next.js + React + TypeScript | 15.4.6 + 19 + 5.6.3 |
| **UI Components** | shadcn/ui + Radix UI + Tailwind CSS | Latest |
| **State Management** | TanStack Query + Zustand | Latest |
| **Forms** | React Hook Form + Zod | Latest |
| **Auth** | Supabase Auth (JWT-based) | Latest |
| **Database** | Supabase PostgreSQL | Latest |
| **Backend** | Python + FastAPI | 3.11+ |
| **AI Services** | Azure Cognitive Services + Google Gemini | Latest |
| **Reviews API** | Google Places API + Playwright fallback | Latest |
| **Email** | Resend API | Latest |
| **Hosting** | Vercel (frontend) + Render/Azure (backend) | Latest |
| **Internationalization** | next-intl | Latest |

## Architecture Overview

### Multi-Layer Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │  External APIs  │
│   (Next.js)     │ ──▶│   (FastAPI)     │ ──▶│  - Google Places│
│                 │    │                 │    │  - Azure AI     │
└─────────────────┘    └─────────────────┘    │  - Gemini       │
         │                       │             │  - Resend       │
         ▼                       ▼             └─────────────────┘
┌─────────────────┐    ┌─────────────────┐
│  Authentication │    │ Database        │
│  (Supabase JWT) │    │ (Supabase PG)   │
└─────────────────┘    └─────────────────┘
```

### Key Components
1. **Frontend (Next.js)** - Dashboard with review feed, sentiment charts, AI summaries, internationalization
2. **Backend (FastAPI)** - API layer with service architecture for Google Places integration and AI analysis
3. **Database (Supabase)** - Stores users, businesses, reviews, sentiment results, and generated summaries
4. **AI Services** - Sentiment analysis and summary generation with fallback mechanisms
5. **Authentication** - JWT-based auth through Supabase with user data isolation

## Development Commands

### Frontend Commands
```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production

# Code Quality (MUST RUN BEFORE COMMITS)
npm run check-all       # TypeScript + ESLint + Prettier check
npm run type-check      # TypeScript compilation check
npm run lint           # ESLint check
npm run lint:fix       # Fix ESLint issues
npm run format         # Format with Prettier
npm run format:check   # Check Prettier formatting
```

### Backend Commands
```bash
# Development
python -m uvicorn app.main:app --reload  # Start development server

# Dependencies
pip install -r requirements.txt          # Install dependencies

# Testing
pytest                                   # Run tests
```

## Key Data Flow

1. **User Authentication** → Supabase Auth validates JWT tokens
2. **Business Onboarding** → User inputs Google Maps URL → Backend parses and validates via Google Places API
3. **Review Fetching** → Backend fetches reviews from Google Places API → Stores in Supabase
4. **AI Processing** → Reviews sent to Azure/Gemini for sentiment analysis → Results stored
5. **Summarization** → AI generates summaries from sentiment data → Stored for reporting
6. **Frontend Display** → TanStack Query fetches data → Displays with real-time updates
7. **Email Reports** → Scheduled jobs generate and send via Resend API

## Key Integration Patterns

### Frontend API Integration
```typescript
// ✅ ALWAYS use custom hooks with TanStack Query
const { businesses, isLoading, error } = useBusinesses();

// ❌ NEVER use direct fetch
fetch('/api/businesses').then(res => res.json())
```

### Backend Service Pattern
```python
# ✅ ALWAYS use service layer pattern
@router.post("/search-business")
async def search_business(
    request: SearchRequest,
    current_user = Depends(get_current_user),
    service: BusinessService = Depends(get_business_service)
):
    return await service.search_business(request, current_user["id"])
```

### Authentication Pattern
```typescript
// Frontend - Always use Supabase client
const { data, error } = await supabase.auth.signInWithPassword({
  email: formData.email,
  password: formData.password
})

// Backend - Always validate JWT
async def get_current_user(user = Depends(verify_supabase_token)):
    return {"id": user.id, "email": user.email}
```

## Critical Integration Points

- **Google Places API**: Rate limiting (100ms delays), proper error handling, 3-month review filtering
- **AI Services**: Azure primary + Gemini fallback, timeout handling, confidence scoring
- **Supabase**: JWT validation, Row Level Security, user data isolation, real-time subscriptions
- **Resend**: HTML email templates, delivery tracking, user preferences
- **Internationalization**: next-intl with EN/TR/NL support, parameter interpolation, fallback handling

## Performance & Security Requirements

### Performance
- Handle 5k+ reviews without degradation
- TanStack Query caching for optimal frontend performance
- Async/await patterns for non-blocking backend operations
- Database query optimization with proper indexing
- Image optimization with Next.js Image component

### Security
- JWT-based authentication with token validation
- User data isolation with proper filtering
- HTTPS-only communication in production
- Environment variable management for secrets
- Rate limiting on external API calls
- GDPR compliance for EU users

## Documentation Structure

- `docs/prd.md` - Product Requirements Document
- `docs/user-stories.md` - Functional requirements and user stories
- `docs/TECH_STACK.md` - Detailed technology analysis and rationale
- `docs/FRONTEND_DEVELOPMENT_GUIDE.md` - Complete frontend development standards
- `docs/BACKEND_DEVELOPMENT_GUIDE.md` - Complete backend development standards
- `frontend/docs/FORM_SYSTEM.md` - Form handling with React Hook Form + Zod
- `frontend/docs/I18N_IMPLEMENTATION.md` - Internationalization implementation details

## Development Workflow

1. **Read Documentation** - Review relevant guides before starting
2. **Follow Patterns** - Use established patterns for consistency
3. **Run Quality Checks** - `npm run check-all` for frontend changes
4. **Test Thoroughly** - Multiple languages, error states, edge cases
5. **Security First** - Always validate auth and isolate user data
6. **Performance Aware** - Use proper async patterns and caching
7. **Document Changes** - Update guides when adding new patterns

---

**Remember**: This is a production SaaS application serving SME customers. Code quality, security, and user experience are paramount. Always follow the established patterns and run quality checks before committing changes.