#!/usr/bin/env tsx

/**
 * Test script for the business scraper function
 * 
 * This script tests the scrapeBusiness function with various queries
 * and verifies that businesses are properly saved to the database.
 * 
 * Usage:
 * npm run test:business-scraper
 * or
 * npx tsx scripts/test-business-scraper.ts
 */

import { config } from 'dotenv';
import { ApifyScraper } from '../src/lib/services/apify';
import { DatabaseService } from '../src/lib/services/database';

// Load environment variables
config({ path: '.env.local' });

interface TestCase {
  name: string;
  query: string;
  expectedFields: string[];
}

const testCases: TestCase[] = [
  {
    name: "Pizza Restaurant",
    query: "Joe's Pizza New York",
    expectedFields: ['business_name', 'google_place_id', 'business_address']
  },
  {
    name: "Coffee Shop",
    query: "Starbucks Times Square New York",
    expectedFields: ['business_name', 'google_place_id', 'rating']
  },
  {
    name: "Hotel",
    query: "Marriott Hotel Manhattan",
    expectedFields: ['business_name', 'google_place_id', 'website']
  },
  {
    name: "Restaurant with Location",
    query: "Italian restaurant in Boston",
    expectedFields: ['business_name', 'google_place_id', 'categories']
  }
];

async function testBusinessScraper() {
  console.log('🧪 Starting Business Scraper Tests');
  console.log('=====================================\n');

  // Check environment variables
  const apifyToken = process.env.APIFY_API_TOKEN;
  if (!apifyToken) {
    console.error('❌ APIFY_API_TOKEN environment variable is required');
    process.exit(1);
  }

  // Initialize services
  const scraper = new ApifyScraper(apifyToken);
  const database = new DatabaseService(true);

  let passedTests = 0;
  let failedTests = 0;

  for (const testCase of testCases) {
    console.log(`🔍 Testing: ${testCase.name}`);
    console.log(`Query: "${testCase.query}"`);
    
    try {
      // Test the scraper
      const startTime = Date.now();
      const businessId = await scraper.scrapeBusiness(testCase.query);
      const endTime = Date.now();
      
      console.log(`✅ Business scraped successfully!`);
      console.log(`   Business ID: ${businessId}`);
      console.log(`   Time taken: ${endTime - startTime}ms`);
      
      // Verify the business was saved correctly
      const business = await database.getBusinessById(businessId);
      
      if (!business) {
        throw new Error('Business not found in database after scraping');
      }
      
      console.log(`   Business Name: ${business.business_name}`);
      console.log(`   Place ID: ${business.google_place_id}`);
      console.log(`   Address: ${business.business_address || 'N/A'}`);
      console.log(`   Rating: ${business.rating || 'N/A'}`);
      console.log(`   Total Reviews: ${business.total_reviews || 'N/A'}`);
      console.log(`   Categories: ${business.categories ? JSON.stringify(business.categories) : 'N/A'}`);
      console.log(`   Original Search URL: ${business.original_search_url || 'N/A'}`);
      
      // Check expected fields
      let missingFields = [];
      for (const field of testCase.expectedFields) {
        if (!business[field as keyof typeof business]) {
          missingFields.push(field);
        }
      }
      
      if (missingFields.length > 0) {
        console.log(`⚠️  Missing expected fields: ${missingFields.join(', ')}`);
      } else {
        console.log(`✅ All expected fields present`);
      }
      
      passedTests++;
      
    } catch (error) {
      console.error(`❌ Test failed:`, error);
      failedTests++;
    }
    
    console.log(''); // Empty line for readability
    
    // Add delay between tests to avoid rate limiting
    if (testCase !== testCases[testCases.length - 1]) {
      console.log('⏳ Waiting 3 seconds before next test...\n');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // Summary
  console.log('📊 Test Summary');
  console.log('================');
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Success Rate: ${((passedTests / testCases.length) * 100).toFixed(1)}%`);
  
  if (failedTests === 0) {
    console.log('\n🎉 All tests passed! Business scraper is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the errors above.');
    process.exit(1);
  }
}

// Test with user profile creation
async function testWithUserProfile() {
  console.log('\n🧪 Testing Business Scraper with User Profile');
  console.log('==============================================\n');

  const apifyToken = process.env.APIFY_API_TOKEN;
  if (!apifyToken) {
    console.error('❌ APIFY_API_TOKEN environment variable is required');
    return;
  }

  const scraper = new ApifyScraper(apifyToken);
  const testUserId = 'test-user-123';
  
  try {
    const businessId = await scraper.scrapeBusiness(
      "McDonald's Times Square New York",
      testUserId
    );
    
    console.log(`✅ Business scraped with user profile`);
    console.log(`   Business ID: ${businessId}`);
    console.log(`   User ID: ${testUserId}`);
    
    // Verify business profile was created
    const database = new DatabaseService(true);
    const profile = await database.getBusinessProfile(testUserId, businessId);
    
    if (profile) {
      console.log(`✅ Business profile created successfully`);
    } else {
      console.log(`❌ Business profile not found`);
    }
    
  } catch (error) {
    console.error(`❌ Test with user profile failed:`, error);
  }
}

// Run tests
async function main() {
  try {
    await testBusinessScraper();
    await testWithUserProfile();
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

export { testBusinessScraper, testWithUserProfile };
