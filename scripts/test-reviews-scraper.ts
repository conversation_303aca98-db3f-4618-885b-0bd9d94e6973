#!/usr/bin/env tsx

/**
 * Test script for the reviews scraper function
 * 
 * This script tests the scrapeReviewsForBusiness function by:
 * 1. Creating test businesses first
 * 2. Scraping reviews for those businesses using their database IDs
 * 3. Verifying reviews are properly saved to the database
 * 
 * Usage:
 * npm run test:reviews-scraper
 * or
 * npx tsx scripts/test-reviews-scraper.ts
 */

import { config } from 'dotenv';
import { ApifyScraper } from '../src/lib/services/apify';
import { DatabaseService } from '../src/lib/services/database';

// Load environment variables
config({ path: '.env.local' });

interface TestBusiness {
  name: string;
  query: string;
  expectedMinReviews: number;
}

const testBusinesses: TestBusiness[] = [
  {
    name: "Popular Restaurant",
    query: "McDonald's Times Square New York",
    expectedMinReviews: 10
  },
  {
    name: "Coffee Chain",
    query: "Starbucks 42nd Street New York",
    expectedMinReviews: 5
  },
  {
    name: "Hotel",
    query: "<PERSON>riott Marquis New York",
    expectedMinReviews: 15
  }
];

async function setupTestBusinesses(scraper: ApifyScraper): Promise<string[]> {
  console.log('🏗️  Setting up test businesses...\n');
  
  const businessIds: string[] = [];
  
  for (const testBusiness of testBusinesses) {
    try {
      console.log(`📍 Creating business: ${testBusiness.name}`);
      console.log(`   Query: "${testBusiness.query}"`);
      
      const businessId = await scraper.scrapeBusiness(testBusiness.query);
      businessIds.push(businessId);
      
      console.log(`✅ Business created with ID: ${businessId}\n`);
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error(`❌ Failed to create business "${testBusiness.name}":`, error);
    }
  }
  
  return businessIds;
}

async function testReviewsScraper() {
  console.log('🧪 Starting Reviews Scraper Tests');
  console.log('==================================\n');

  // Check environment variables
  const apifyToken = process.env.APIFY_API_TOKEN;
  if (!apifyToken) {
    console.error('❌ APIFY_API_TOKEN environment variable is required');
    process.exit(1);
  }

  // Initialize services
  const scraper = new ApifyScraper(apifyToken);
  const database = new DatabaseService(true);

  try {
    // Step 1: Setup test businesses
    const businessIds = await setupTestBusinesses(scraper);
    
    if (businessIds.length === 0) {
      console.error('❌ No test businesses were created. Cannot proceed with reviews testing.');
      process.exit(1);
    }
    
    console.log(`✅ Created ${businessIds.length} test businesses`);
    console.log(`Business IDs: ${businessIds.join(', ')}\n`);

    // Step 2: Test reviews scraping
    console.log('🔍 Testing reviews scraping...');
    
    const startTime = Date.now();
    await scraper.scrapeReviewsForBusiness(businessIds, 20); // Limit to 20 reviews per business for testing
    const endTime = Date.now();
    
    console.log(`✅ Reviews scraping completed!`);
    console.log(`   Time taken: ${endTime - startTime}ms\n`);

    // Step 3: Verify reviews were saved
    console.log('🔍 Verifying saved reviews...\n');
    
    let totalReviews = 0;
    let businessesWithReviews = 0;
    
    for (let i = 0; i < businessIds.length; i++) {
      const businessId = businessIds[i];
      const testBusiness = testBusinesses[i];
      
      try {
        // Get business details
        const business = await database.getBusinessById(businessId);
        if (!business) {
          console.log(`❌ Business not found: ${businessId}`);
          continue;
        }
        
        console.log(`📊 Business: ${business.business_name}`);
        console.log(`   Place ID: ${business.google_place_id}`);
        console.log(`   Original Search URL: ${business.original_search_url}`);
        
        // Get reviews for this business
        const reviews = await database.getRecentReviews(business.google_place_id, 100);
        
        console.log(`   Reviews found: ${reviews.length}`);
        
        if (reviews.length > 0) {
          businessesWithReviews++;
          totalReviews += reviews.length;
          
          // Show sample review
          const sampleReview = reviews[0];
          console.log(`   Sample review:`);
          console.log(`     Author: ${sampleReview.author_name || 'Anonymous'}`);
          console.log(`     Rating: ${sampleReview.rating}/5`);
          console.log(`     Text: ${sampleReview.review_text.substring(0, 100)}...`);
          console.log(`     Date: ${sampleReview.review_date}`);
          
          // Check if minimum expected reviews were found
          if (reviews.length >= testBusiness.expectedMinReviews) {
            console.log(`   ✅ Expected minimum reviews met (${testBusiness.expectedMinReviews})`);
          } else {
            console.log(`   ⚠️  Below expected minimum reviews (${testBusiness.expectedMinReviews})`);
          }
        } else {
          console.log(`   ⚠️  No reviews found for this business`);
        }
        
        console.log(''); // Empty line for readability
        
      } catch (error) {
        console.error(`❌ Error verifying business ${businessId}:`, error);
      }
    }

    // Step 4: Summary
    console.log('📊 Test Summary');
    console.log('================');
    console.log(`🏢 Businesses tested: ${businessIds.length}`);
    console.log(`📝 Businesses with reviews: ${businessesWithReviews}`);
    console.log(`📊 Total reviews scraped: ${totalReviews}`);
    console.log(`📈 Average reviews per business: ${businessIds.length > 0 ? (totalReviews / businessIds.length).toFixed(1) : 0}`);
    console.log(`✅ Success rate: ${((businessesWithReviews / businessIds.length) * 100).toFixed(1)}%`);
    
    if (businessesWithReviews === businessIds.length) {
      console.log('\n🎉 All businesses have reviews! Reviews scraper is working correctly.');
    } else if (businessesWithReviews > 0) {
      console.log('\n⚠️  Some businesses have no reviews. This might be normal for businesses with few reviews.');
    } else {
      console.log('\n❌ No reviews were found for any business. Please check the scraper configuration.');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Reviews scraper test failed:', error);
    process.exit(1);
  }
}

// Test individual business reviews
async function testSingleBusinessReviews() {
  console.log('\n🧪 Testing Single Business Reviews');
  console.log('===================================\n');

  const apifyToken = process.env.APIFY_API_TOKEN;
  if (!apifyToken) {
    console.error('❌ APIFY_API_TOKEN environment variable is required');
    return;
  }

  const scraper = new ApifyScraper(apifyToken);
  
  try {
    // Create a single test business
    console.log('📍 Creating test business...');
    const businessId = await scraper.scrapeBusiness("Shake Shack Madison Square Park New York");
    console.log(`✅ Business created: ${businessId}`);
    
    // Scrape reviews for just this business
    console.log('🔍 Scraping reviews...');
    await scraper.scrapeReviewsForBusiness([businessId], 10);
    
    // Verify results
    const database = new DatabaseService(true);
    const business = await database.getBusinessById(businessId);
    
    if (business) {
      const reviews = await database.getRecentReviews(business.google_place_id, 50);
      console.log(`✅ Found ${reviews.length} reviews for ${business.business_name}`);
      
      if (reviews.length > 0) {
        console.log('📝 Sample reviews:');
        reviews.slice(0, 3).forEach((review, index) => {
          console.log(`   ${index + 1}. ${review.author_name}: ${review.rating}/5 - "${review.review_text.substring(0, 80)}..."`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Single business test failed:', error);
  }
}

// Run tests
async function main() {
  try {
    await testReviewsScraper();
    await testSingleBusinessReviews();
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

export { testReviewsScraper, testSingleBusinessReviews };
