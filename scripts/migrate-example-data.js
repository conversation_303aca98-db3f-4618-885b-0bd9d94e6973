#!/usr/bin/env node

/**
 * Migration script to populate database with example data
 * This script reads the example data from /src/data and inserts it into Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  log('❌ Missing required environment variables:', colors.red);
  log('   NEXT_PUBLIC_SUPABASE_URL', colors.red);
  log('   SUPABASE_SERVICE_ROLE_KEY', colors.red);
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Helper function to extract place ID from Google Maps URL
function extractPlaceId(url) {
  const match = url.match(/query_place_id=([A-Za-z0-9_-]+)/);
  return match ? match[1] : null;
}

// Helper function to combine address parts
function formatAddress(business) {
  const parts = [];
  if (business.street) parts.push(business.street);
  if (business.city) parts.push(business.city);
  if (business.state) parts.push(business.state);
  if (business.countryCode) parts.push(business.countryCode);
  return parts.join(', ');
}

// Helper function to generate random sentiment
function getRandomSentiment() {
  const sentiments = ['positive', 'negative', 'neutral'];
  const weights = [0.6, 0.2, 0.2]; // 60% positive, 20% negative, 20% neutral
  const random = Math.random();
  let cumulative = 0;
  
  for (let i = 0; i < sentiments.length; i++) {
    cumulative += weights[i];
    if (random <= cumulative) {
      return sentiments[i];
    }
  }
  return 'neutral';
}

// Helper function to determine sentiment from rating
function getSentimentFromRating(rating) {
  if (rating >= 4) return 'positive';
  if (rating <= 2) return 'negative';
  return 'neutral';
}

async function createTestUser() {
  log('👤 Creating test user...', colors.blue);
  
  // Create a test user using Supabase Auth Admin API
  const { data: user, error } = await supabase.auth.admin.createUser({
    email: '<EMAIL>',
    password: 'testpassword123',
    email_confirm: true,
    user_metadata: {
      name: 'Test User',
      role: 'user'
    }
  });

  if (error) {
    if (error.message.includes('already registered') || error.code === 'email_exists') {
      log('✓ Test user already exists', colors.green);
      // Get existing user
      const { data: existingUser } = await supabase.auth.admin.listUsers();
      const testUser = existingUser.users.find(u => u.email === '<EMAIL>');
      return testUser?.id;
    } else {
      log(`❌ Error creating test user: ${error.message}`, colors.red);
      throw error;
    }
  }

  log(`✓ Test user created with ID: ${user.user.id}`, colors.green);
  return user.user.id;
}

async function migrateBusinesses() {
  log('🏢 Migrating businesses...', colors.blue);
  
  const businessesPath = path.resolve('src/data/fetchBusinessesFromApify.json');
  const businessesData = JSON.parse(fs.readFileSync(businessesPath, 'utf8'));
  
  const businesses = [];
  let inserted = 0;
  let skipped = 0;

  for (const business of businessesData) {
    const placeId = extractPlaceId(business.url);
    if (!placeId) {
      log(`⚠️ Could not extract place ID for: ${business.title}`, colors.yellow);
      skipped++;
      continue;
    }

    const businessData = {
      google_place_id: placeId,
      business_name: business.title,
      business_address: formatAddress(business),
      phone_number: business.phone,
      website: business.website,
    };

    // Check if business already exists
    const { data: existing } = await supabase
      .from('businesses')
      .select('id')
      .eq('google_place_id', placeId)
      .single();

    if (existing) {
      log(`⏭️ Business already exists: ${business.title}`, colors.yellow);
      businesses.push({ ...businessData, id: existing.id });
      skipped++;
      continue;
    }

    // Insert new business
    const { data: newBusiness, error } = await supabase
      .from('businesses')
      .insert(businessData)
      .select()
      .single();

    if (error) {
      log(`❌ Error inserting business ${business.title}: ${error.message}`, colors.red);
      continue;
    }

    businesses.push(newBusiness);
    inserted++;
    log(`✓ Inserted: ${business.title}`, colors.green);
  }

  log(`📊 Businesses migration complete:`, colors.cyan);
  log(`   Inserted: ${inserted}`, colors.green);
  log(`   Skipped: ${skipped}`, colors.yellow);
  log(`   Total: ${businesses.length}`, colors.blue);

  return businesses;
}

async function migrateReviews(userId, businesses) {
  log('📝 Migrating reviews...', colors.blue);

  const reviewsPath = path.resolve('src/data/fetchReviewsFromApify.json');
  const reviewsData = JSON.parse(fs.readFileSync(reviewsPath, 'utf8'));

  // Clear existing reviews for this user to re-migrate with new schema
  log('🧹 Clearing existing reviews for fresh migration...', colors.yellow);
  const { error: deleteError } = await supabase
    .from('reviews')
    .delete()
    .eq('user_id', userId);

  if (deleteError) {
    log(`⚠️ Warning: Could not clear existing reviews: ${deleteError.message}`, colors.yellow);
  }

  // For demo purposes, we'll assign reviews to random businesses
  // In real scenario, reviews would be linked to specific place IDs

  let inserted = 0;
  let skipped = 0;

  for (const review of reviewsData) {
    if (!review.review || review.review.trim() === '') {
      skipped++;
      continue;
    }

    // Randomly assign to a business (for demo purposes)
    const randomBusiness = businesses[Math.floor(Math.random() * businesses.length)];
    
    // Process photo URLs from images array
    const photoUrls = review.images ? review.images.map(img => ({
      id: img.id,
      url: img.url,
      width: img.size?.width || null,
      height: img.size?.height || null,
      location: img.location || null,
      caption: img.caption || null
    })) : [];

    const reviewData = {
      user_id: userId,
      google_place_id: randomBusiness.google_place_id,
      google_review_id: review.id,
      review_text: review.review,
      rating: review.rating,
      author_name: review.author?.name || 'Anonymous',
      author_avatar: review.author?.avatar || null,
      author_url: review.author?.url || null,
      author_id: review.author?.id || null,
      review_date: review.publishedAt,
      updated_review_date: review.updatedAt && review.updatedAt !== review.publishedAt ? review.updatedAt : null,
      photo_urls: photoUrls,
      language: review.language || null,
      original_language: review.language || null,
      translated: false,
      likes: review.likes || 0,
      reply: review.reply || null,
      sentiment: getSentimentFromRating(review.rating),
    };

    // Check if review already exists
    const { data: existing } = await supabase
      .from('reviews')
      .select('id')
      .eq('google_review_id', review.id)
      .single();

    if (existing) {
      skipped++;
      continue;
    }

    // Insert new review
    const { error } = await supabase
      .from('reviews')
      .insert(reviewData);

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        skipped++;
        continue;
      }
      log(`❌ Error inserting review: ${error.message}`, colors.red);
      continue;
    }

    inserted++;
    if (inserted % 10 === 0) {
      log(`✓ Inserted ${inserted} reviews...`, colors.green);
    }
  }

  log(`📊 Reviews migration complete:`, colors.cyan);
  log(`   Inserted: ${inserted}`, colors.green);
  log(`   Skipped: ${skipped}`, colors.yellow);

  return inserted;
}

async function createBusinessProfiles(userId, businesses) {
  log('🔗 Creating business profiles...', colors.blue);
  
  // Create business profiles for first 10 businesses (for demo)
  const businessesToProfile = businesses.slice(0, 10);
  let created = 0;

  for (const business of businessesToProfile) {
    const { error } = await supabase
      .from('business_profiles')
      .insert({
        user_id: userId,
        business_id: business.id,
      });

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        continue;
      }
      log(`❌ Error creating business profile: ${error.message}`, colors.red);
      continue;
    }

    created++;
  }

  log(`✓ Created ${created} business profiles`, colors.green);
  return created;
}

async function generateSampleSummary(userId) {
  log('📊 Generating sample summary...', colors.blue);
  
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - 1); // Last month

  const summaryData = {
    user_id: userId,
    period_start: startDate.toISOString(),
    period_end: endDate.toISOString(),
    positive_themes: [
      'Excellent food quality',
      'Great customer service',
      'Beautiful ambiance',
      'Good value for money'
    ],
    negative_themes: [
      'Long waiting times',
      'Limited parking',
      'Noisy environment'
    ],
    recommended_improvements: [
      'Improve reservation system',
      'Add more parking spaces',
      'Better noise control',
      'Faster service during peak hours'
    ],
    total_reviews: 150,
    sentiment_distribution: {
      positive: 90,
      negative: 30,
      neutral: 30
    }
  };

  const { error } = await supabase
    .from('summaries')
    .insert(summaryData);

  if (error) {
    log(`❌ Error creating summary: ${error.message}`, colors.red);
    return false;
  }

  log('✓ Sample summary created', colors.green);
  return true;
}

async function main() {
  try {
    log('🚀 Starting migration of example data...', colors.bright);
    
    // Step 1: Create test user
    const userId = await createTestUser();
    
    // Step 2: Migrate businesses
    const businesses = await migrateBusinesses();
    
    // Step 3: Migrate reviews
    const reviewsCount = await migrateReviews(userId, businesses);
    
    // Step 4: Create business profiles
    const profilesCount = await createBusinessProfiles(userId, businesses);
    
    // Step 5: Generate sample summary
    await generateSampleSummary(userId);
    
    log('🎉 Migration completed successfully!', colors.green);
    log('📊 Summary:', colors.cyan);
    log(`   Test User ID: ${userId}`, colors.blue);
    log(`   Businesses: ${businesses.length}`, colors.blue);
    log(`   Reviews: ${reviewsCount}`, colors.blue);
    log(`   Business Profiles: ${profilesCount}`, colors.blue);
    log('', colors.reset);
    log('💡 You can now:', colors.yellow);
    log('   - Login with: <EMAIL> / testpassword123', colors.yellow);
    log('   - View the migrated data in your application', colors.yellow);
    log('   - Test the review analysis features', colors.yellow);
    
  } catch (error) {
    log(`❌ Migration failed: ${error.message}`, colors.red);
    console.error(error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  main();
}

module.exports = {
  createTestUser,
  migrateBusinesses,
  migrateReviews,
  createBusinessProfiles,
  generateSampleSummary
};
