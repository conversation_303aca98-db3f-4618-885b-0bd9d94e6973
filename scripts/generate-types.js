#!/usr/bin/env node

/**
 * <PERSON>ript to generate Supabase types from the remote database
 * This script handles authentication and type generation automatically
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const PROJECT_ID = 'evauqytvhvjuryhhfjoy';
const OUTPUT_FILE = 'src/types/supabase.ts';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    return result;
  } catch (error) {
    if (!options.silent) {
      log(`Error executing command: ${command}`, colors.red);
      log(error.message, colors.red);
    }
    throw error;
  }
}

function checkSupabaseCLI() {
  try {
    execCommand('supabase --version', { silent: true });
    log('✓ Supabase CLI is installed', colors.green);
    return true;
  } catch (error) {
    log('✗ Supabase CLI is not installed', colors.red);
    log('Please install it with: npm install -g supabase', colors.yellow);
    return false;
  }
}

function generateTypesFromRemote() {
  log('🔄 Generating types from remote Supabase project...', colors.blue);
  
  try {
    // Generate types using project ID
    const command = `supabase gen types typescript --project-id ${PROJECT_ID} --schema public`;
    log(`Executing: ${command}`, colors.cyan);
    
    const types = execCommand(command, { silent: true });
    
    // Write types to file
    const outputPath = path.resolve(OUTPUT_FILE);
    fs.writeFileSync(outputPath, types);
    
    log(`✓ Types generated successfully at ${outputPath}`, colors.green);
    return true;
  } catch (error) {
    log('✗ Failed to generate types from remote project', colors.red);
    log('This might be due to authentication issues', colors.yellow);
    return false;
  }
}

function generateTypesFromSchema() {
  log('🔄 Generating types from local schema file...', colors.blue);
  
  const schemaPath = path.resolve('database/schema.sql');
  
  if (!fs.existsSync(schemaPath)) {
    log(`✗ Schema file not found at ${schemaPath}`, colors.red);
    return false;
  }
  
  try {
    // Create a temporary database and generate types
    log('Creating temporary database from schema...', colors.cyan);
    
    // This is a simplified approach - in practice, you might want to use a more robust method
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');
    
    // Generate basic types from schema (this is a fallback method)
    const basicTypes = generateBasicTypesFromSchema(schemaContent);
    
    const outputPath = path.resolve(OUTPUT_FILE);
    fs.writeFileSync(outputPath, basicTypes);
    
    log(`✓ Basic types generated from schema at ${outputPath}`, colors.green);
    log('⚠️  For full type generation, please ensure Supabase CLI authentication', colors.yellow);
    return true;
  } catch (error) {
    log('✗ Failed to generate types from schema', colors.red);
    log(error.message, colors.red);
    return false;
  }
}

function generateBasicTypesFromSchema(schemaContent) {
  // This is a basic type generator - for production, use the actual Supabase CLI
  return `export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      // Generated from schema.sql
      // For full type generation, run: npm run types:generate
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Helper types
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Note: This is a basic type structure. 
// Run 'npm run types:generate' with proper Supabase authentication for full types.
`;
}

function addHelperTypes() {
  const outputPath = path.resolve(OUTPUT_FILE);
  
  if (!fs.existsSync(outputPath)) {
    log('✗ Types file not found, cannot add helper types', colors.red);
    return false;
  }
  
  let content = fs.readFileSync(outputPath, 'utf8');
  
  // Check if helper types already exist
  if (content.includes('// Helper types for easier usage')) {
    log('✓ Helper types already exist', colors.green);
    return true;
  }
  
  const helperTypes = `

// Helper types for easier usage
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]

// Specific table types for convenience
export type Business = Tables<'businesses'>
export type BusinessInsert = TablesInsert<'businesses'>
export type BusinessUpdate = TablesUpdate<'businesses'>

export type BusinessProfile = Tables<'business_profiles'>
export type BusinessProfileInsert = TablesInsert<'business_profiles'>
export type BusinessProfileUpdate = TablesUpdate<'business_profiles'>

export type Review = Tables<'reviews'>
export type ReviewInsert = TablesInsert<'reviews'>
export type ReviewUpdate = TablesUpdate<'reviews'>

export type Summary = Tables<'summaries'>
export type SummaryInsert = TablesInsert<'summaries'>
export type SummaryUpdate = TablesUpdate<'summaries'>

export type EmailReport = Tables<'email_reports'>
export type EmailReportInsert = TablesInsert<'email_reports'>
export type EmailReportUpdate = TablesUpdate<'email_reports'>

// Extended types with relationships
export type BusinessProfileWithBusiness = BusinessProfile & {
  businesses: Business
}

// Utility types for API responses
export type SupabaseResponse<T> = {
  data: T | null
  error: Error | null
}

export type SupabaseArrayResponse<T> = {
  data: T[] | null
  error: Error | null
  count?: number | null
}
`;
  
  content += helperTypes;
  fs.writeFileSync(outputPath, content);
  
  log('✓ Helper types added successfully', colors.green);
  return true;
}

function main() {
  log('🚀 Starting Supabase type generation...', colors.bright);
  
  // Check if Supabase CLI is installed
  if (!checkSupabaseCLI()) {
    process.exit(1);
  }
  
  // Try to generate types from remote first
  let success = generateTypesFromRemote();
  
  // If remote generation fails, try from schema
  if (!success) {
    log('Falling back to schema-based generation...', colors.yellow);
    success = generateTypesFromSchema();
  }
  
  if (success) {
    // Add helper types
    addHelperTypes();
    
    log('🎉 Type generation completed successfully!', colors.green);
    log(`📁 Types saved to: ${OUTPUT_FILE}`, colors.cyan);
    log('💡 You can now import types from @/types/supabase', colors.blue);
  } else {
    log('❌ Type generation failed', colors.red);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  generateTypesFromRemote,
  generateTypesFromSchema,
  addHelperTypes
};
