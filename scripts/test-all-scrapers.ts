#!/usr/bin/env tsx

/**
 * Comprehensive test script for both Apify scrapers
 * 
 * This script runs a complete test suite for:
 * 1. Business scraper (scrapeBusiness function)
 * 2. Reviews scraper (scrapeReviewsForBusiness function)
 * 3. Integration tests between both scrapers
 * 
 * Usage:
 * npm run test:scrapers
 * or
 * npx tsx scripts/test-all-scrapers.ts
 */

import { config } from 'dotenv';
import { ApifyScraper } from '../src/lib/services/apify';
import { DatabaseService } from '../src/lib/services/database';

// Load environment variables
config({ path: '.env.local' });

interface TestResult {
  testName: string;
  passed: boolean;
  duration: number;
  details?: string;
  error?: string;
}

class ScraperTestSuite {
  private scraper: ApifyScraper;
  private database: DatabaseService;
  private results: TestResult[] = [];

  constructor(apifyToken: string) {
    this.scraper = new ApifyScraper(apifyToken);
    this.database = new DatabaseService(true);
  }

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<TestResult> {
    console.log(`🧪 Running: ${testName}`);
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      console.log(`✅ ${testName} - PASSED (${duration}ms)\n`);
      
      return {
        testName,
        passed: true,
        duration,
        details: `Completed successfully in ${duration}ms`
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ ${testName} - FAILED (${duration}ms)`);
      console.error(`   Error: ${errorMessage}\n`);
      
      return {
        testName,
        passed: false,
        duration,
        error: errorMessage
      };
    }
  }

  async testBusinessScraper(): Promise<void> {
    const result = await this.runTest('Business Scraper - Basic Functionality', async () => {
      const businessId = await this.scraper.scrapeBusiness("Starbucks Union Square New York");
      
      if (!businessId) {
        throw new Error('No business ID returned');
      }
      
      const business = await this.database.getBusinessById(businessId);
      if (!business) {
        throw new Error('Business not found in database');
      }
      
      // Verify required fields
      if (!business.business_name || !business.google_place_id || !business.original_search_url) {
        throw new Error('Missing required business fields');
      }
      
      console.log(`   ✓ Business: ${business.business_name}`);
      console.log(`   ✓ Place ID: ${business.google_place_id}`);
      console.log(`   ✓ Search URL: ${business.original_search_url}`);
    });
    
    this.results.push(result);
  }

  async testBusinessScraperWithUser(): Promise<void> {
    const result = await this.runTest('Business Scraper - With User Profile', async () => {
      const testUserId = 'test-user-' + Date.now();
      const businessId = await this.scraper.scrapeBusiness(
        "McDonald's Herald Square New York",
        testUserId
      );
      
      if (!businessId) {
        throw new Error('No business ID returned');
      }
      
      // Verify business profile was created
      const profile = await this.database.getBusinessProfile(testUserId, businessId);
      if (!profile) {
        throw new Error('Business profile not created');
      }
      
      console.log(`   ✓ Business ID: ${businessId}`);
      console.log(`   ✓ User ID: ${testUserId}`);
      console.log(`   ✓ Profile created: ${profile.id}`);
    });
    
    this.results.push(result);
  }

  async testReviewsScraper(): Promise<void> {
    const result = await this.runTest('Reviews Scraper - Basic Functionality', async () => {
      // First create a business
      const businessId = await this.scraper.scrapeBusiness("Shake Shack Madison Square Park");
      
      // Then scrape reviews for it
      await this.scraper.scrapeReviewsForBusiness([businessId], 10);
      
      // Verify reviews were saved
      const business = await this.database.getBusinessById(businessId);
      if (!business) {
        throw new Error('Business not found');
      }
      
      const reviews = await this.database.getRecentReviews(business.google_place_id, 50);
      
      if (reviews.length === 0) {
        throw new Error('No reviews were scraped');
      }
      
      // Verify review structure
      const sampleReview = reviews[0];
      if (!sampleReview.google_review_id || !sampleReview.review_text || !sampleReview.rating) {
        throw new Error('Review missing required fields');
      }
      
      console.log(`   ✓ Business: ${business.business_name}`);
      console.log(`   ✓ Reviews scraped: ${reviews.length}`);
      console.log(`   ✓ Sample review: ${sampleReview.rating}/5 by ${sampleReview.author_name}`);
    });
    
    this.results.push(result);
  }

  async testMultipleBusinessReviews(): Promise<void> {
    const result = await this.runTest('Reviews Scraper - Multiple Businesses', async () => {
      // Create multiple businesses
      const queries = [
        "Joe's Pizza Greenwich Village",
        "Katz's Delicatessen New York"
      ];
      
      const businessIds: string[] = [];
      for (const query of queries) {
        const businessId = await this.scraper.scrapeBusiness(query);
        businessIds.push(businessId);
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // Scrape reviews for all businesses
      await this.scraper.scrapeReviewsForBusiness(businessIds, 5);
      
      // Verify results
      let totalReviews = 0;
      for (const businessId of businessIds) {
        const business = await this.database.getBusinessById(businessId);
        if (business) {
          const reviews = await this.database.getRecentReviews(business.google_place_id, 20);
          totalReviews += reviews.length;
          console.log(`   ✓ ${business.business_name}: ${reviews.length} reviews`);
        }
      }
      
      if (totalReviews === 0) {
        throw new Error('No reviews found for any business');
      }
      
      console.log(`   ✓ Total reviews: ${totalReviews}`);
    });
    
    this.results.push(result);
  }

  async testErrorHandling(): Promise<void> {
    const result = await this.runTest('Error Handling', async () => {
      // Test with invalid business ID
      try {
        await this.scraper.scrapeReviewsForBusiness(['invalid-business-id'], 5);
        throw new Error('Should have thrown error for invalid business ID');
      } catch (error) {
        if (error instanceof Error && error.message.includes('Should have thrown')) {
          throw error;
        }
        // Expected error, continue
      }
      
      // Test with non-existent query
      try {
        await this.scraper.scrapeBusiness("XYZ123NonExistentBusinessQuery456");
        // This might not throw an error if Apify returns something, so we'll just log it
        console.log('   ✓ Non-existent query handled gracefully');
      } catch (error) {
        console.log('   ✓ Non-existent query properly rejected');
      }
      
      console.log('   ✓ Error handling tests completed');
    });
    
    this.results.push(result);
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Comprehensive Scraper Test Suite');
    console.log('==============================================\n');

    // Check environment
    const apifyToken = process.env.APIFY_API_TOKEN;
    if (!apifyToken) {
      throw new Error('APIFY_API_TOKEN environment variable is required');
    }

    // Run all tests
    await this.testBusinessScraper();
    await this.testBusinessScraperWithUser();
    await this.testReviewsScraper();
    await this.testMultipleBusinessReviews();
    await this.testErrorHandling();

    // Generate report
    this.generateReport();
  }

  private generateReport(): void {
    console.log('📊 Test Suite Results');
    console.log('=====================\n');

    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => r.passed === false).length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️  Total Duration: ${totalDuration}ms`);
    console.log(`📈 Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%\n`);

    if (failed > 0) {
      console.log('❌ Failed Tests:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   • ${r.testName}: ${r.error}`);
        });
      console.log('');
    }

    console.log('📋 Detailed Results:');
    this.results.forEach(r => {
      const status = r.passed ? '✅' : '❌';
      console.log(`   ${status} ${r.testName} (${r.duration}ms)`);
    });

    if (failed === 0) {
      console.log('\n🎉 All tests passed! Both scrapers are working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the errors above.');
      process.exit(1);
    }
  }
}

async function main() {
  try {
    const apifyToken = process.env.APIFY_API_TOKEN;
    if (!apifyToken) {
      console.error('❌ APIFY_API_TOKEN environment variable is required');
      console.error('Please add it to your .env.local file');
      process.exit(1);
    }

    const testSuite = new ScraperTestSuite(apifyToken);
    await testSuite.runAllTests();
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

export { ScraperTestSuite };
