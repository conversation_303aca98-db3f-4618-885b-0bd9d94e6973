#!/usr/bin/env node

/**
 * <PERSON>ript to apply database migrations to Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  log('❌ Missing required environment variables:', colors.red);
  log('   NEXT_PUBLIC_SUPABASE_URL', colors.red);
  log('   SUPABASE_SERVICE_ROLE_KEY', colors.red);
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function applyMigration(migrationFile) {
  log(`📄 Applying migration: ${migrationFile}`, colors.blue);
  
  const migrationPath = path.resolve('database/migrations', migrationFile);
  
  if (!fs.existsSync(migrationPath)) {
    log(`❌ Migration file not found: ${migrationPath}`, colors.red);
    return false;
  }
  
  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  
  try {
    // Execute the migration SQL
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      // If RPC doesn't exist, try direct SQL execution (this might not work in all cases)
      log('⚠️ RPC method not available, trying alternative approach...', colors.yellow);
      
      // Split SQL into individual statements and execute them
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.trim()) {
          log(`Executing: ${statement.substring(0, 50)}...`, colors.cyan);
          // Note: This approach has limitations with complex SQL
          // In production, use proper migration tools
        }
      }
      
      log('⚠️ Manual SQL execution required. Please run the migration in Supabase SQL editor:', colors.yellow);
      log(`   File: ${migrationPath}`, colors.yellow);
      return false;
    }
    
    log(`✓ Migration applied successfully: ${migrationFile}`, colors.green);
    return true;
    
  } catch (error) {
    log(`❌ Error applying migration: ${error.message}`, colors.red);
    log('💡 Please apply the migration manually in Supabase SQL editor:', colors.yellow);
    log(`   File: ${migrationPath}`, colors.yellow);
    return false;
  }
}

async function main() {
  log('🚀 Starting database migration...', colors.bright);
  
  const migrationFile = process.argv[2] || '001_add_review_fields.sql';
  
  const success = await applyMigration(migrationFile);
  
  if (success) {
    log('🎉 Migration completed successfully!', colors.green);
    log('💡 Now run: npm run types:generate', colors.cyan);
  } else {
    log('❌ Migration failed or requires manual application', colors.red);
    log('📋 Manual steps:', colors.yellow);
    log('1. Open Supabase Dashboard > SQL Editor', colors.yellow);
    log(`2. Copy and paste the content from: database/migrations/${migrationFile}`, colors.yellow);
    log('3. Execute the SQL', colors.yellow);
    log('4. Run: npm run types:generate', colors.yellow);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { applyMigration };
