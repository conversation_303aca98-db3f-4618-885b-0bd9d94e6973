#!/bin/bash

# Development Setup Script for Review Pulse
# This script sets up the development environment and generates Supabase types

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the frontend directory."
    exit 1
fi

print_status "🚀 Setting up Review Pulse development environment..."

# Check Node.js version
NODE_VERSION=$(node --version)
print_status "Node.js version: $NODE_VERSION"

# Install dependencies
print_status "📦 Installing dependencies..."
npm install

# Check if Supabase CLI is installed globally
if ! command -v supabase &> /dev/null; then
    print_warning "Supabase CLI not found globally. Installing..."
    npm install -g supabase
else
    print_success "Supabase CLI is already installed"
fi

# Check environment variables
print_status "🔧 Checking environment variables..."

if [ ! -f ".env.local" ]; then
    print_warning ".env.local not found. Creating from template..."
    cp .env.local.example .env.local 2>/dev/null || print_warning "No .env.local.example found"
fi

# Required environment variables
REQUIRED_VARS=(
    "NEXT_PUBLIC_SUPABASE_URL"
    "NEXT_PUBLIC_SUPABASE_ANON_KEY"
    "SUPABASE_SERVICE_ROLE_KEY"
    "APIFY_API_TOKEN"
)

MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ] && ! grep -q "^$var=" .env.local 2>/dev/null; then
        MISSING_VARS+=("$var")
    fi
done

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    print_warning "Missing environment variables in .env.local:"
    for var in "${MISSING_VARS[@]}"; do
        echo "  - $var"
    done
    print_warning "Please add these variables to .env.local before running the application"
fi

# Generate Supabase types
print_status "🔄 Generating Supabase types..."
npm run types:generate

if [ $? -eq 0 ]; then
    print_success "Supabase types generated successfully"
else
    print_warning "Failed to generate Supabase types. You may need to authenticate with Supabase CLI"
    print_status "To authenticate, run: supabase login"
fi

# Run type checking
print_status "🔍 Running type check..."
npm run type-check

if [ $? -eq 0 ]; then
    print_success "Type check passed"
else
    print_warning "Type check failed. Please fix TypeScript errors before proceeding"
fi

# Run linting
print_status "🧹 Running linter..."
npm run lint

if [ $? -eq 0 ]; then
    print_success "Linting passed"
else
    print_warning "Linting issues found. Run 'npm run lint:fix' to auto-fix some issues"
fi

print_success "🎉 Development environment setup complete!"
print_status ""
print_status "Next steps:"
print_status "1. Review and update .env.local with your API keys"
print_status "2. Run 'npm run dev' to start the development server"
print_status "3. Visit http://localhost:3000 to see your application"
print_status ""
print_status "Useful commands:"
print_status "  npm run dev              - Start development server"
print_status "  npm run types:generate   - Regenerate Supabase types"
print_status "  npm run lint:fix         - Fix linting issues"
print_status "  npm run check-all        - Run all checks (types, lint, format)"
print_status ""
