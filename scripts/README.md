# Apify Scraper Test Scripts

This directory contains comprehensive test scripts for the simplified Apify scraper service.

## Overview

The scraper service has been simplified to include only 2 main functions:

1. **`scrapeBusiness(query: string, userId?: string)`** - Scrapes business information and saves to database
2. **`scrapeReviewsForBusiness(businessIds: string[], maxReviews?: number)`** - Scrapes reviews for businesses using their database IDs

## Key Changes

### Reviews Scraper Updates
- Now accepts **business IDs** from our database instead of Google Place IDs
- Queries the database to get the `original_search_url` for each business
- Uses the full Google Maps URL for the Apify reviews endpoint
- Automatically handles URL-based review scraping

### Business Scraper Updates
- Stores `original_search_query` and `original_search_url` in the database
- Creates business profiles when `userId` is provided
- Generates Google Maps URLs for review scraping

## Test Scripts

### 1. Business Scraper Test (`test-business-scraper.ts`)

Tests the `scrapeBusiness` function with various business types:

```bash
npm run test:business-scraper
```

**What it tests:**
- Pizza restaurants, coffee shops, hotels, etc.
- Business data validation (name, place ID, address, etc.)
- User profile creation
- Database storage verification

### 2. Reviews Scraper Test (`test-reviews-scraper.ts`)

Tests the `scrapeReviewsForBusiness` function:

```bash
npm run test:reviews-scraper
```

**What it tests:**
- Creates test businesses first
- Scrapes reviews using business IDs
- Verifies reviews are saved to database
- Tests multiple businesses at once
- Validates review data structure

### 3. Comprehensive Test Suite (`test-all-scrapers.ts`)

Runs a complete test suite for both scrapers:

```bash
npm run test:scrapers
```

**What it tests:**
- All business scraper functionality
- All reviews scraper functionality
- Integration between both scrapers
- Error handling scenarios
- Performance metrics

## Prerequisites

### Environment Variables

Create a `.env.local` file with:

```env
# Required
APIFY_API_TOKEN=your_apify_api_token_here

# Database (should already be configured)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Dependencies

Make sure you have `tsx` installed for running TypeScript scripts:

```bash
npm install -g tsx
# or use npx tsx
```

## Running Tests

### Individual Tests

```bash
# Test business scraper only
npm run test:business-scraper

# Test reviews scraper only  
npm run test:reviews-scraper

# Run all tests
npm run test:scrapers
```

### Direct Execution

```bash
# Using tsx directly
npx tsx scripts/test-business-scraper.ts
npx tsx scripts/test-reviews-scraper.ts
npx tsx scripts/test-all-scrapers.ts
```

## Test Output

### Successful Test Output

```
🧪 Starting Business Scraper Tests
=====================================

🔍 Testing: Pizza Restaurant
Query: "Joe's Pizza New York"
✅ Business scraped successfully!
   Business ID: 123e4567-e89b-12d3-a456-426614174000
   Time taken: 3245ms
   Business Name: Joe's Pizza
   Place ID: ChIJN1t_tDeuEmsRUsoyG83frY4
   Address: 7 Carmine St, New York, NY 10014
   Rating: 4.2
   Total Reviews: 1247
   Categories: ["restaurant", "pizza_restaurant"]
   Original Search URL: https://maps.google.com/maps?cid=ChIJN1t_tDeuEmsRUsoyG83frY4
✅ All expected fields present

📊 Test Summary
================
✅ Passed: 4
❌ Failed: 0
📈 Success Rate: 100.0%

🎉 All tests passed! Business scraper is working correctly.
```

### Error Handling

The tests include comprehensive error handling and will show:
- Network errors
- API rate limiting
- Invalid responses
- Database connection issues
- Missing environment variables

## Usage Examples

### Basic Business Scraping

```typescript
import { ApifyScraper } from '../src/lib/services/apify';

const scraper = new ApifyScraper(process.env.APIFY_API_TOKEN!);

// Scrape a business
const businessId = await scraper.scrapeBusiness("Starbucks Times Square");

// Scrape with user profile
const businessId = await scraper.scrapeBusiness(
  "McDonald's Herald Square", 
  "user-123"
);
```

### Reviews Scraping

```typescript
// Scrape reviews for multiple businesses
const businessIds = ["business-id-1", "business-id-2"];
await scraper.scrapeReviewsForBusiness(businessIds, 50);

// The scraper will:
// 1. Query database for each business ID
// 2. Get the original_search_url for each business  
// 3. Use that URL to scrape reviews from Apify
// 4. Save reviews to database
```

## Troubleshooting

### Common Issues

1. **Missing APIFY_API_TOKEN**
   ```
   ❌ APIFY_API_TOKEN environment variable is required
   ```
   Solution: Add your Apify API token to `.env.local`

2. **Database Connection Issues**
   ```
   ❌ Failed to create business: connection error
   ```
   Solution: Check your Supabase credentials in `.env.local`

3. **Rate Limiting**
   ```
   ❌ Error fetching business from Apify: rate limit exceeded
   ```
   Solution: The tests include delays between requests. Wait and retry.

4. **No Reviews Found**
   ```
   ⚠️ No reviews found for business: Business Name
   ```
   This is normal for businesses with very few reviews.

### Debug Mode

For more detailed logging, you can modify the test scripts to include debug output by uncommenting console.log statements or adding your own.

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Include proper error handling
3. Add delays between API calls to avoid rate limiting
4. Verify database state after operations
5. Update this README with new test descriptions
