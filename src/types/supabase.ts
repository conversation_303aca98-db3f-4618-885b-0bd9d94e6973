export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      business_profiles: {
        Row: {
          business_id: string | null
          created_at: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          business_id?: string | null
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          business_id?: string | null
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "business_profiles_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_profiles_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_needing_six_month_update"
            referencedColumns: ["id"]
          },
        ]
      }
      businesses: {
        Row: {
          business_address: string | null
          business_name: string
          categories: Json | null
          created_at: string | null
          google_place_id: string
          id: string
          last_fetch_time: string | null
          original_search_query: string | null
          original_search_url: string | null
          phone_number: string | null
          rating: number | null
          six_month_count_last_updated: string | null
          six_month_review_count: number | null
          total_reviews: number | null
          updated_at: string | null
          website: string | null
        }
        Insert: {
          business_address?: string | null
          business_name: string
          categories?: Json | null
          created_at?: string | null
          google_place_id: string
          id?: string
          last_fetch_time?: string | null
          original_search_query?: string | null
          original_search_url?: string | null
          phone_number?: string | null
          rating?: number | null
          six_month_count_last_updated?: string | null
          six_month_review_count?: number | null
          total_reviews?: number | null
          updated_at?: string | null
          website?: string | null
        }
        Update: {
          business_address?: string | null
          business_name?: string
          categories?: Json | null
          created_at?: string | null
          google_place_id?: string
          id?: string
          last_fetch_time?: string | null
          original_search_query?: string | null
          original_search_url?: string | null
          phone_number?: string | null
          rating?: number | null
          six_month_count_last_updated?: string | null
          six_month_review_count?: number | null
          total_reviews?: number | null
          updated_at?: string | null
          website?: string | null
        }
        Relationships: []
      }
      email_reports: {
        Row: {
          analysis_data: Json
          business_name: string
          created_at: string | null
          id: string
          recipient_email: string
          report_type: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          analysis_data: Json
          business_name: string
          created_at?: string | null
          id?: string
          recipient_email: string
          report_type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          analysis_data?: Json
          business_name?: string
          created_at?: string | null
          id?: string
          recipient_email?: string
          report_type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      reviews: {
        Row: {
          author_avatar: string | null
          author_id: string | null
          author_name: string | null
          author_url: string | null
          created_at: string | null
          google_place_id: string
          google_review_id: string
          id: string
          language: string | null
          likes: number | null
          original_language: string | null
          photo_urls: Json | null
          rating: number | null
          reply: string | null
          review_date: string
          review_text: string
          sentiment: Database["public"]["Enums"]["sentiment_type"] | null
          translated: boolean | null
          updated_at: string | null
          updated_review_date: string | null
          user_id: string | null
        }
        Insert: {
          author_avatar?: string | null
          author_id?: string | null
          author_name?: string | null
          author_url?: string | null
          created_at?: string | null
          google_place_id: string
          google_review_id: string
          id?: string
          language?: string | null
          likes?: number | null
          original_language?: string | null
          photo_urls?: Json | null
          rating?: number | null
          reply?: string | null
          review_date: string
          review_text: string
          sentiment?: Database["public"]["Enums"]["sentiment_type"] | null
          translated?: boolean | null
          updated_at?: string | null
          updated_review_date?: string | null
          user_id?: string | null
        }
        Update: {
          author_avatar?: string | null
          author_id?: string | null
          author_name?: string | null
          author_url?: string | null
          created_at?: string | null
          google_place_id?: string
          google_review_id?: string
          id?: string
          language?: string | null
          likes?: number | null
          original_language?: string | null
          photo_urls?: Json | null
          rating?: number | null
          reply?: string | null
          review_date?: string
          review_text?: string
          sentiment?: Database["public"]["Enums"]["sentiment_type"] | null
          translated?: boolean | null
          updated_at?: string | null
          updated_review_date?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      summaries: {
        Row: {
          created_at: string | null
          id: string
          negative_themes: Json | null
          period_end: string
          period_start: string
          positive_themes: Json | null
          recommended_improvements: Json | null
          sentiment_distribution: Json | null
          total_reviews: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          negative_themes?: Json | null
          period_end: string
          period_start: string
          positive_themes?: Json | null
          recommended_improvements?: Json | null
          sentiment_distribution?: Json | null
          total_reviews?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          negative_themes?: Json | null
          period_end?: string
          period_start?: string
          positive_themes?: Json | null
          recommended_improvements?: Json | null
          sentiment_distribution?: Json | null
          total_reviews?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_profiles: {
        Row: {
          business_description: string | null
          business_role: string | null
          created_at: string | null
          full_name: string | null
          id: string
          onboarding_completed: boolean | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          business_description?: string | null
          business_role?: string | null
          created_at?: string | null
          full_name?: string | null
          id?: string
          onboarding_completed?: boolean | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          business_description?: string | null
          business_role?: string | null
          created_at?: string | null
          full_name?: string | null
          id?: string
          onboarding_completed?: boolean | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      businesses_needing_six_month_update: {
        Row: {
          business_name: string | null
          google_place_id: string | null
          id: string | null
          original_search_query: string | null
          original_search_url: string | null
          six_month_count_last_updated: string | null
          six_month_review_count: number | null
        }
        Insert: {
          business_name?: string | null
          google_place_id?: string | null
          id?: string | null
          original_search_query?: string | null
          original_search_url?: string | null
          six_month_count_last_updated?: string | null
          six_month_review_count?: number | null
        }
        Update: {
          business_name?: string | null
          google_place_id?: string | null
          id?: string | null
          original_search_query?: string | null
          original_search_url?: string | null
          six_month_count_last_updated?: string | null
          six_month_review_count?: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      needs_six_month_update: {
        Args: { last_updated: string }
        Returns: boolean
      }
      uuid_generate_v1: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v1mc: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v3: {
        Args: { name: string; namespace: string }
        Returns: string
      }
      uuid_generate_v4: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_generate_v5: {
        Args: { name: string; namespace: string }
        Returns: string
      }
      uuid_nil: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_dns: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_oid: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_url: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uuid_ns_x500: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
    }
    Enums: {
      sentiment_type: "positive" | "negative" | "neutral"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      sentiment_type: ["positive", "negative", "neutral"],
    },
  },
} as const


// Helper types for easier usage
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]

// Specific table types for convenience
export type Business = Tables<'businesses'>
export type BusinessInsert = TablesInsert<'businesses'>
export type BusinessUpdate = TablesUpdate<'businesses'>

export type BusinessProfile = Tables<'business_profiles'>
export type BusinessProfileInsert = TablesInsert<'business_profiles'>
export type BusinessProfileUpdate = TablesUpdate<'business_profiles'>

export type Review = Tables<'reviews'>
export type ReviewInsert = TablesInsert<'reviews'>
export type ReviewUpdate = TablesUpdate<'reviews'>

export type Summary = Tables<'summaries'>
export type SummaryInsert = TablesInsert<'summaries'>
export type SummaryUpdate = TablesUpdate<'summaries'>

export type EmailReport = Tables<'email_reports'>
export type EmailReportInsert = TablesInsert<'email_reports'>
export type EmailReportUpdate = TablesUpdate<'email_reports'>

// Extended types with relationships
export type BusinessProfileWithBusiness = BusinessProfile & {
  businesses: Business
}

// Utility types for API responses
export type SupabaseResponse<T> = {
  data: T | null
  error: Error | null
}

export type SupabaseArrayResponse<T> = {
  data: T[] | null
  error: Error | null
  count?: number | null
}
