// API request types
import { type Locale } from '@/i18n/config';

export interface GoogleMapsUrlRequest {
  google_maps_url: string;
  language?: Locale;
}

export interface GooglePlaceRequest {
  google_place_id: string;
  business_name?: string;
  language?: Locale;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface BusinessSearchRequest {
  searchQuery: string;
  locationQuery?: string;
  language?: Locale;
  maxResults?: number;
}

export interface ReviewsFetchRequest {
  place_id: string;
  max_reviews?: number;
  language?: Locale;
  reviews_sort?: 'newest' | 'oldest' | 'highest_rating' | 'lowest_rating';
}