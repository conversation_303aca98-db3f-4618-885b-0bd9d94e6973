// Reviews-related API types

export interface Review {
  id: string;
  user_id: string;
  google_place_id: string;
  review_text: string;
  rating: number;
  author_name?: string;
  review_date: string;
  google_review_id: string;
  photo_urls?: string[];
  language?: string;
  original_language?: string;
  translated?: boolean;
  sentiment?: 'positive' | 'negative' | 'neutral';
  created_at: string;
}

export interface PaginatedReviewsResponse {
  reviews: Review[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
  average_rating?: number;
}
