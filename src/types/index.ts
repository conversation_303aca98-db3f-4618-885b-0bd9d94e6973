// Database Types
export interface User {
  id: string;
  email: string;
  created_at: string;
}

export interface Business {
  id: string;
  google_place_id: string;
  business_name: string;
  business_address?: string;
  phone_number?: string;
  website?: string;
  last_fetch_time?: string;
  rating?: number;
  total_reviews?: number;
  categories?: string[];
  created_at: string;
  updated_at?: string;
}

export interface BusinessProfile {
  id: string;
  user_id: string;
  business_id: string;
  created_at: string;
  businesses?: Business;
}

export interface UserProfile {
  id: string;
  user_id: string;
  full_name?: string;
  business_role?: string;
  business_description?: string;
  onboarding_completed: boolean;
  created_at: string;
  updated_at: string;
}

export interface Review {
  id: string;
  user_id: string;
  google_place_id: string;
  review_text: string;
  rating: number;
  author_name?: string;
  review_date: string;
  google_review_id: string;
  photo_urls?: string[];
  language?: string;
  original_language?: string;
  translated?: boolean;
  sentiment?: SentimentType;
  created_at: string;
}

export interface Summary {
  id: string;
  user_id: string;
  period_start: string;
  period_end: string;
  positive_themes: string[];
  negative_themes: string[];
  recommended_improvements: string[];
  total_reviews: number;
  sentiment_distribution: Record<string, number>;
  created_at: string;
}

// Enums
export enum SentimentType {
  POSITIVE = "positive",
  NEGATIVE = "negative",
  NEUTRAL = "neutral"
}

// Re-export API types for convenience
export type {
  GoogleMapsUrlRequest,
  GooglePlaceRequest,
  PaginationParams,
  BusinessSearchRequest,
  ReviewsFetchRequest,
} from './api';

// Apify Types
export interface ApifyBusinessInfo {
  placeId: string;
  title: string;
  address?: string;
  phone?: string;
  website?: string;
  rating?: number;
  reviewsCount?: number;
  categories?: string[];
  // Additional properties for location-based searches
  url?: string;
  street?: string;
  city?: string;
  state?: string;
  countryCode?: string;
  totalScore?: number; // Alias for rating
}

export interface ApifyReview {
  reviewId: string;
  text: string;
  rating: number;
  authorName?: string;
  authorAvatar?: string;
  authorUrl?: string;
  authorId?: string;
  publishedAtDate: string;
  updatedAtDate?: string;
  photos?: string[];
  language?: string;
  likes?: number;
  reply?: string;
}

// Configuration Types
export interface AppConfig {
  supabase: {
    url: string;
    anonKey: string;
    serviceRoleKey: string;
  };
  apify: {
    apiToken: string;
  };
  ai: {
    azure?: {
      endpoint: string;
      key: string;
    };
    gemini?: {
      apiKey: string;
    };
  };
  rateLimitSeconds: number;
}

// Error Types
export interface ApiError {
  message: string;
  status: number;
  details?: Record<string, unknown>;
}

// Auth Types
export interface AuthUser {
  id: string;
  email: string;
  aud: string;
  role?: string;
}

// Rate Limiting Types
export interface RateLimitStatus {
  can_fetch: boolean;
  message: string;
  last_fetch_time?: string;
  next_available_time?: string;
}
