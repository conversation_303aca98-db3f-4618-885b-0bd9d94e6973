"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/api";

interface FetchReviewsRequest {
  google_place_id: string;
  business_name?: string;
}

interface FetchReviewsResponse {
  message: string;
  business_name: string;
  business_id: string;
  place_id: string;
  total_reviews: number;
  new_reviews: number;
  existing_reviews: number;
  last_fetch_time: string;
}

export function useFetchReviews() {
  const queryClient = useQueryClient();

  return useMutation<FetchReviewsResponse, Error, FetchReviewsRequest>({
    mutationFn: async ({ google_place_id, business_name }) => {
      return apiClient.fetchReviews(google_place_id, business_name);
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({
        queryKey: ["reviews", variables.google_place_id],
      });
      queryClient.invalidateQueries({
        queryKey: ["paginatedReviews", variables.google_place_id],
      });
      queryClient.invalidateQueries({
        queryKey: ["rateLimit", variables.google_place_id],
      });

      console.log("Reviews fetched successfully:", data);
    },
    onError: error => {
      console.error("Error fetching reviews:", error);
    },
  });
}
