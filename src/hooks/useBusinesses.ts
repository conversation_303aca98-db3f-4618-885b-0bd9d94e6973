"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/api";
import { useAuth } from "@/contexts/AuthContext";

export function useBusinesses() {
  const queryClient = useQueryClient();
  const { user, loading: authLoading } = useAuth();

  // Get saved businesses
  const {
    data: businesses = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["businesses"],
    queryFn: () => apiClient.getMyBusinesses(),
    enabled: !authLoading && !!user, // Only run when auth is loaded and user exists
    retry: 1,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  // Debug logging (can be removed in production)
  console.log("useBusinesses:", {
    authLoading,
    userExists: !!user,
    businessCount: businesses.length,
    isLoading,
    error: error?.message
  });

  // Search business from URL
  const searchFromUrlMutation = useMutation({
    mutationFn: apiClient.searchBusinessFromUrl,
  });

  // Save business
  const saveBusinessMutation = useMutation({
    mutationFn: apiClient.saveBusiness,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["businesses"] });
    },
  });

  // Remove business
  const removeBusinessMutation = useMutation({
    mutationFn: apiClient.removeBusiness,
    onSuccess: () => {
  
      queryClient.invalidateQueries({ queryKey: ["businesses"] });
    },
  });

  // This functionality is now handled by the reviews API
  // Remove the fetchReviewsByBusiness mutation as it's no longer needed

  return {
    // Data
    businesses,
    isLoading,
    error,

    // Actions
    searchFromUrl: searchFromUrlMutation.mutateAsync,
    saveBusiness: saveBusinessMutation.mutateAsync,
    removeBusiness: removeBusinessMutation.mutateAsync,
    refetch,

    // Loading states
    isSearching: searchFromUrlMutation.isPending,
    isSaving: saveBusinessMutation.isPending,
    isRemoving: removeBusinessMutation.isPending,

    // Errors
    searchError: searchFromUrlMutation.error,
    saveError: saveBusinessMutation.error,
    removeError: removeBusinessMutation.error,
  };
}
