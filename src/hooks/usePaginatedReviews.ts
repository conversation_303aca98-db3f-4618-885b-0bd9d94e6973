"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { apiClient } from "@/lib/api";

// Use the types from the API module
import type { PaginatedReviewsResponse } from "@/types/api";

export interface ReviewFilters {
  google_place_id?: string;
  start_date?: string;
  end_date?: string;
  sentiment?: string;
}

export interface UsePaginatedReviewsOptions {
  page?: number;
  limit?: number;
  filters?: ReviewFilters;
  autoFetch?: boolean;
}

export function usePaginatedReviews(options: UsePaginatedReviewsOptions = {}) {
  const {
    page: initialPage = 1,
    limit = 10,
    filters = {},
    autoFetch = true,
  } = options;

  const [data, setData] = useState<PaginatedReviewsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const lastFiltersRef = useRef<string>("");

  const fetchReviews = useCallback(
    async (page: number) => {
      // If google_place_id is required but not provided, don't fetch
      if (filters.google_place_id === undefined || filters.google_place_id === null || filters.google_place_id === "") {
        setData(null);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const params = {
          page,
          limit,
          ...Object.fromEntries(
            Object.entries(filters).filter(
              ([_, value]) => value !== undefined && value !== ""
            )
          ),
        };

        const result = await apiClient.getReviewsPaginated(params);
        setData(result);
        setCurrentPage(page);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch reviews"
        );
        console.error("Error fetching paginated reviews:", err);
      } finally {
        setLoading(false);
      }
    },
    [limit, filters] // Include all filters in dependencies
  );

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= (data?.total_pages || 1)) {
      fetchReviews(page);
    }
  }, [fetchReviews, data?.total_pages]);

  const nextPage = useCallback(() => {
    if (data?.has_next) {
      goToPage(currentPage + 1);
    }
  }, [data?.has_next, goToPage, currentPage]);

  const previousPage = useCallback(() => {
    if (data?.has_previous) {
      goToPage(currentPage - 1);
    }
  }, [data?.has_previous, goToPage, currentPage]);

  const refresh = useCallback(() => {
    fetchReviews(currentPage);
  }, [fetchReviews, currentPage]);

  // Auto-fetch on mount and when filters change - use stable reference and prevent unnecessary calls
  const filtersString = JSON.stringify(filters);
  useEffect(() => {
    if (autoFetch && filtersString !== lastFiltersRef.current) {
      lastFiltersRef.current = filtersString;
      setData(null); // Clear previous data when filters change
      fetchReviews(1);
    }
  }, [filtersString, limit, autoFetch, fetchReviews]); // Include fetchReviews in dependencies

  return {
    data,
    loading,
    error,
    currentPage,
    fetchReviews,
    goToPage,
    nextPage,
    previousPage,
    refresh,
    // Convenience properties
    reviews: data?.reviews || [],
    total: data?.total || 0,
    totalPages: data?.total_pages || 1,
    hasNext: data?.has_next || false,
    hasPrevious: data?.has_previous || false,
    averageRating: data?.average_rating,
  };
}
