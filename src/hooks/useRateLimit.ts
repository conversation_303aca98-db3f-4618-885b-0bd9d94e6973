"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";

export interface RateLimitStatus {
  can_fetch: boolean;
  message: string;
  last_fetch_time?: string;
  next_available_time?: string;
}

// Simple cache to prevent excessive API calls
const rateLimitCache = new Map<string, { data: RateLimitStatus; timestamp: number }>();
const CACHE_TTL = 30000; // 30 seconds cache

export function useRateLimit(googlePlaceId?: string) {
  const { session } = useAuth();
  const [status, setStatus] = useState<RateLimitStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastPlaceIdRef = useRef<string | undefined>(undefined);

  const checkRateLimit = useCallback(
    async (placeId?: string) => {
      const targetPlaceId = placeId || googlePlaceId;

      if (!session?.access_token || !targetPlaceId) {
        setError("Missing authentication token or place ID");
        return null;
      }

      // Check cache first to prevent excessive API calls
      const cacheKey = `${targetPlaceId}-${session.access_token}`;
      const cached = rateLimitCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        setStatus(cached.data);
        return cached.data;
      }

      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams({
          google_place_id: targetPlaceId,
        });

        const apiUrl =
          process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

        const response = await fetch(
          `${apiUrl}/api/reviews/rate-limit-status?${params}`,
          {
            headers: {
              Authorization: `Bearer ${session.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`Failed to check rate limit: ${response.statusText}`);
        }

        const result = await response.json();

        // Cache the result
        rateLimitCache.set(cacheKey, { data: result, timestamp: Date.now() });

        setStatus(result);
        return result;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to check rate limit";
        setError(errorMessage);
        console.error("Error checking rate limit:", err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [session?.access_token] // Remove googlePlaceId from dependencies to prevent infinite loops
  );

  // Auto-check when googlePlaceId changes - use stable reference and prevent unnecessary calls
  useEffect(() => {
    if (googlePlaceId && session?.access_token && googlePlaceId !== lastPlaceIdRef.current) {
      lastPlaceIdRef.current = googlePlaceId;
      checkRateLimit(googlePlaceId);
    }
  }, [googlePlaceId, session?.access_token]); // Remove checkRateLimit from dependencies

  const getTimeUntilNextFetch = () => {
    if (!status?.next_available_time) return null;

    const nextTime = new Date(status.next_available_time);
    const now = new Date();
    const diff = nextTime.getTime() - now.getTime();

    if (diff <= 0) return null;

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const formatLastFetchTime = () => {
    if (!status?.last_fetch_time) return null;

    const lastFetch = new Date(status.last_fetch_time);
    const now = new Date();
    const diff = now.getTime() - lastFetch.getTime();

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m ago`;
    } else if (minutes > 0) {
      return `${minutes}m ago`;
    } else {
      return "Just now";
    }
  };

  return {
    status,
    loading,
    error,
    checkRateLimit,
    canFetch: status?.can_fetch ?? true,
    message: status?.message,
    timeUntilNextFetch: getTimeUntilNextFetch(),
    lastFetchTime: formatLastFetchTime(),
  };
}
