import { useMutation } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import type { BusinessSearchResponse } from '@/types/api';

/**
 * Hook for searching businesses with automatic language detection
 * This demonstrates how the language-aware API integration works
 */
export function useBusinessSearchWithLanguage() {

  return useMutation({
    mutationFn: async (googleMapsUrl: string): Promise<BusinessSearchResponse> => {
      console.log(`📍 URL: ${googleMapsUrl}`);
      
      // The apiClient.searchBusinessFromUrl method now automatically
      // includes the user's language preference
      const result = await apiClient.searchBusinessFromUrl(googleMapsUrl);
      
      console.log(`✅ Found business: ${result.name}`);
      return result;
    },
    onError: (error) => {
      console.error('❌ Business search failed:', error);
    },
    onSuccess: (data) => {
      console.log(`🎉 Business search successful for: ${data.name}`);
    },
  });
}

/**
 * Example usage in a React component:
 * 
 * ```tsx
 * import { useBusinessSearchWithLanguage } from '@/hooks/useBusinessSearchWithLanguage';
 * 
 * function BusinessSearchComponent() {
 *   const searchBusiness = useBusinessSearchWithLanguage();
 * 
 *   const handleSearch = (url: string) => {
 *     searchBusiness.mutate(url);
 *   };
 * 
 *   return (
 *     <div>
 *       {searchBusiness.isPending && <p>Searching...</p>}
 *       {searchBusiness.error && <p>Error: {searchBusiness.error.message}</p>}
 *       {searchBusiness.data && <p>Found: {searchBusiness.data.name}</p>}
 *     </div>
 *   );
 * }
 * ```
 */