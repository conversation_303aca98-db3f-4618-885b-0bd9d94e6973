"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { UserProfile } from "@/types";
import { apiClient } from "@/lib/api";

interface ProfileUpdateData {
  full_name?: string;
  business_role?: string;
  business_description?: string;
  onboarding_completed?: boolean;
}

export function useUserProfile() {
  const queryClient = useQueryClient();

  // Get user profile
  const {
    data: profile,
    isLoading,
    error,
    refetch,
  } = useQuery<UserProfile | null>({
    queryKey: ["userProfile"],
    queryFn: async () => {
      try {
        const data = await apiClient.get('/api/user/profile');
        return data.profile;
      } catch (error: any) {
        if (error.message.includes('401')) {
          return null;
        }
        // If the table doesn't exist yet, return a fallback profile
        if (error.message.includes('500')) {
          console.warn('User profiles table may not exist yet. Using fallback.');
          return null;
        }
        throw error;
      }
    },
    retry: false, // Don't retry on errors
    refetchOnWindowFocus: false, // Don't refetch on window focus
  });

  // Update user profile
  const updateProfileMutation = useMutation({
    mutationFn: async (profileData: ProfileUpdateData) => {
      const data = await apiClient.post('/api/user/profile', profileData);
      return data.profile;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userProfile"] });
    },
  });

  return {
    // Data
    profile,
    isLoading,
    error,
    hasCompletedOnboarding: profile?.onboarding_completed ?? false,

    // Actions
    updateProfile: updateProfileMutation.mutateAsync,
    refetch,

    // Loading states
    isUpdating: updateProfileMutation.isPending,

    // Errors
    updateError: updateProfileMutation.error,
  };
}