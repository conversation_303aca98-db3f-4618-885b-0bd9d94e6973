import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/api";
import type { Summary } from "@/types/api";

export function useSummaries() {
  return useQuery({
    queryKey: ["summaries"],
    queryFn: apiClient.getSummaries,
  });
}

export function useGenerateSummary() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      google_place_id,
      period_start,
      period_end,
      business_name,
    }: {
      google_place_id: string;
      period_start: string;
      period_end: string;
      business_name?: string;
    }) =>
      apiClient.generateSummary({
        google_place_id,
        period_start,
        period_end,
        business_name,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["summaries"] });
    },
  });
}
