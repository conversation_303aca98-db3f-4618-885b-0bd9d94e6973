{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "confirm": "Confirm", "tryAgain": "Try Again", "website": "Website", "phone": "Phone", "email": "Email", "address": "Address", "name": "Name", "description": "Description", "category": "Category", "date": "Date", "time": "Time", "status": "Status", "actions": "Actions", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact", "privacy": "Privacy", "terms": "Terms", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "dashboard": "Dashboard", "home": "Home", "yes": "Yes", "no": "No", "optional": "Optional", "required": "Required"}, "validation": {"email": {"required": "Email is required", "invalid": "Please enter a valid email address", "tooLong": "Email is too long"}, "password": {"required": "Password is required", "tooShort": "Password must be at least {min} characters", "tooLong": "Password is too long", "weak": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "mismatch": "Passwords don't match"}, "general": {"required": "{field} is required", "tooShort": "{field} must be at least {min} characters", "tooLong": "{field} is too long", "invalid": "Please enter a valid {field}", "invalidFormat": "Invalid {field} format"}, "url": {"required": "URL is required", "invalid": "Please enter a valid URL", "googleMapsInvalid": "Please enter a valid Google Maps URL"}, "phone": {"invalid": "Please enter a valid phone number"}, "name": {"required": "Name is required", "tooLong": "Name is too long", "invalidChars": "Name contains invalid characters"}, "business": {"nameRequired": "Business name is required", "nameTooLong": "Business name is too long", "addressRequired": "Address is required", "addressTooLong": "Address is too long", "categoryRequired": "Category is required", "descriptionTooLong": "Description is too long"}, "profile": {"companyTooLong": "Company name is too long", "jobTitleTooLong": "Job title is too long"}, "placeId": {"required": "Place ID is required", "invalid": "Invalid Place ID format"}, "terms": {"required": "You must accept the terms and conditions"}}, "forms": {"labels": {"email": "Email address", "password": "Password", "confirmPassword": "Confirm password", "firstName": "First name", "lastName": "Last name", "company": "Company", "jobTitle": "Job title", "industry": "Industry", "bio": "Bio", "newsletter": "Subscribe to newsletter", "notifications": "Enable notifications", "terms": "I accept the terms and conditions", "googleMapsUrl": "Google Maps URL", "placeId": "Google Place ID"}, "placeholders": {"email": "Enter your email", "password": "Enter your password", "confirmPassword": "Confirm your password", "firstName": "Enter your first name", "lastName": "Enter your last name", "company": "Enter your company name", "jobTitle": "Enter your job title", "bio": "Tell us a bit about yourself...", "googleMapsUrl": "https://maps.google.com/maps?place_id=...", "placeId": "Enter Google Place ID", "selectIndustry": "Select your industry"}, "descriptions": {"password": "Must contain at least one uppercase letter, one lowercase letter, and one number", "newsletter": "Receive updates about new features and company news", "notifications": "Get notified about important updates and activities", "terms": "You must accept our terms and conditions to continue", "googleMapsUrl": "Paste a Google Maps link to a business, restaurant, or any reviewable location", "bio": "Optional: Share a brief description about yourself (max 500 characters)"}, "buttons": {"signIn": "Sign in", "signingIn": "Signing in...", "signUp": "Sign up", "createAccount": "Create account", "creatingAccount": "Creating account...", "submit": "Submit", "submitting": "Submitting...", "extractBusinessInfo": "Extract Business Information", "extractingBusinessInfo": "Extracting Business Info...", "confirmAndSave": "Confirm & Save", "saving": "Saving...", "fetchReviews": "Fetch Reviews", "fetchingReviews": "Fetching...", "remove": "Remove", "removing": "Removing..."}}, "pages": {"home": {"loading": "Loading..."}, "auth": {"signInTitle": "Sign in to ReviewPulse", "signUpTitle": "Create your ReviewPulse account", "checkEmailTitle": "Check your email", "checkEmailMessage": "We sent you a confirmation link. Please check your email to activate your account.", "noAccount": "Don't have an account?", "hasAccount": "Already have an account?", "signUpLink": "Sign up", "signInLink": "Sign in", "unexpectedError": "An unexpected error occurred. Please try again."}, "dashboard": {"title": "Review Analytics Dashboard", "appName": "ReviewPulse", "signOut": "Sign out", "tabs": {"myBusinesses": "My Businesses", "addBusiness": "Add Business", "manualEntry": "Manual Entry"}, "analytics": {"totalReviews": "Total Reviews", "averageRating": "Average Rating", "positiveSentiment": "Positive Sentiment", "recentReviews": "Recent Reviews"}, "manualEntry": {"title": "Manual Place ID Entry", "description": "Enter a Google Place ID directly to fetch reviews", "placeIdLabel": "Google Place ID", "placeIdPlaceholder": "Enter Google Place ID", "fetchButton": "Fetch Reviews", "fetchingButton": "Fetching...", "errorPrefix": "Error:"}, "noReviews": "No reviews found for this place ID.", "noReviewsLoaded": "No reviews loaded yet. Add a business or enter a Place ID to get started.", "moreReviews": "And {count} more reviews...", "fetchSuccess": "Successfully fetched {count} reviews for {name}", "fetchError": "Failed to fetch reviews: {error}", "loadingReviews": "Loading reviews..."}, "examples": {"title": "Example Form Components", "subtitle": "Demonstrating shadcn/ui + React Hook Form + Zod integration", "formTitle": "User Registration Form", "sections": {"personalInfo": {"title": "Personal Information", "description": "Please provide your basic information"}, "businessInfo": {"title": "Business Information", "description": "Tell us about your work"}, "preferences": {"title": "Preferences", "description": "Customize your experience"}}, "submitSuccess": "Form submitted successfully!", "submitError": "Failed to submit form. Please try again."}}, "components": {"business": {"addFromGoogleMaps": "Add Business from Google Maps", "confirmDetails": "Confirm Business Details", "isCorrectBusiness": "Is this the correct business?", "confirmMessage": "Once confirmed, you'll be able to fetch and analyze reviews for this location.", "savedBusinesses": "Your Saved Businesses", "noBusinessesSaved": "No Businesses Saved", "noBusinessesMessage": "Add a business using a Google Maps link to get started with review analysis.", "loadingBusinesses": "Loading your saved businesses...", "removeConfirm": "Are you sure you want to remove this business from your saved list?", "failedToLoad": "Failed to load businesses", "failedToRemove": "Failed to remove business", "failedToSave": "Failed to save business", "failedToExtract": "Failed to extract business information", "addedOn": "Added {date}", "lastUpdated": "Last updated", "reviewCount": "reviews", "businessType": "Business", "howToGetLink": "How to get a Google Maps link:", "step1": "1. Go to Google Maps and search for a business", "step2": "2. <PERSON><PERSON> on the business to open its details", "step3": "3. Copy the URL from your browser's address bar", "step4": "4. Paste it here to automatically extract business information", "viewDetails": "View Details", "backToBusinesses": "Back to My Businesses", "businessDetails": "Business Details", "latestReviews": "Latest Reviews", "viewAllReviews": "View All Reviews", "sentimentAnalysis": "Sentiment Analysis", "latestSummaryReport": "Latest Summary Report", "quickActions": "Quick Actions", "viewAnalytics": "View Analytics", "summaryReports": "Summary Reports", "businessInformation": "Business Information", "visitWebsite": "Visit Website", "noReviewsYet": "No reviews available yet", "loadingReviews": "Loading reviews...", "loadingBusinessDetails": "Loading business details...", "businessNotFound": "Business not found", "allReviews": "All Reviews", "completeReviewAnalysis": "Complete review analysis for {businessName}", "totalReviews": "Total Reviews", "averageRating": "Average Rating", "positive": "Positive", "negative": "Negative", "filtersAndSorting": "Filters & Sorting", "sentiment": "Sentiment", "sortBy": "Sort By", "allSentiments": "All Sentiments", "neutral": "Neutral", "newestFirst": "Newest First", "oldestFirst": "Oldest First", "highestRating": "Highest Rating", "lowestRating": "Lowest Rating", "showingResults": "Showing {filtered} of {total} reviews", "noReviewsFound": "No Reviews Found", "noReviewsWithFilters": "No {sentiment} reviews found with current filters.", "noReviewsAvailable": "No reviews available for this business yet.", "updateReviews": "Update Reviews", "updatingReviews": "Fetching...", "updateReviewsTooltip": "Fetch latest reviews from Google", "backToBusiness": "Back to {businessName}", "completeAnalysis": "Complete review analysis for {businessName}", "generatedOn": "Generated on {date}", "viewAllReports": "View All Reports", "noSummaryReports": "No summary reports available yet", "nextUpdateAvailable": "Next update available in: {time}", "fetchReviewsSuccess": "Successfully fetched {count} reviews for {name}", "fetchReviewsError": "Failed to fetch reviews", "authenticationRequired": "Authentication required", "back": "Back"}, "theme": {"toggleTooltip": "Toggle theme", "switchToLight": "Switch to light mode", "switchToDark": "Switch to dark mode", "switchToSystem": "Switch to system theme"}, "language": {"currentLanguage": "Current language: {language}", "switchToNext": "Switch to next language"}}, "industries": {"technology": "Technology", "healthcare": "Healthcare", "finance": "Finance", "education": "Education", "retail": "Retail", "manufacturing": "Manufacturing", "other": "Other"}, "sentiments": {"positive": "positive", "negative": "negative", "neutral": "neutral"}}