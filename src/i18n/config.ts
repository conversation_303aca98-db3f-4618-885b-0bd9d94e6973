import { getRequestConfig } from "next-intl/server";

// Can be imported from a shared config
export const locales = ["en", "tr", "nl"] as const;
export type Locale = (typeof locales)[number];

export const defaultLocale: Locale = "en";

export const localeNames: Record<Locale, string> = {
  en: "English",
  tr: "Türkçe",
  nl: "Nederlands",
};

export const localeFlags: Record<Locale, string> = {
  en: "🇺🇸",
  tr: "🇹🇷",
  nl: "🇳🇱",
};

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  const validLocale = locales.includes(locale as Locale)
    ? (locale as Locale)
    : defaultLocale;

  return {
    locale: validLocale,
    messages: (await import(`./messages/${validLocale}.json`)).default,
  };
});
