"use client";

import React, { createContext, useContext, useMemo } from "react";
import { z } from "zod";
import { useTranslations } from "next-intl";

interface ValidationContextType {
  schemas: {
    loginSchema: z.ZodSchema;
    registerSchema: z.ZodSchema;
    googleMapsUrlSchema: z.ZodSchema;
    businessSchema: z.ZodSchema;
    placeIdSchema: z.ZodSchema;
    profileSchema: z.ZodSchema;
    changePasswordSchema: z.ZodSchema;
  };
}

const ValidationContext = createContext<ValidationContextType | undefined>(
  undefined
);

interface ValidationProviderProps {
  children: React.ReactNode;
}

export function ValidationProvider({ children }: ValidationProviderProps) {
  const t = useTranslations("validation");

  const schemas = useMemo(() => {
    // Common validation utilities with translations
    const commonValidations = {
      email: z
        .string()
        .min(1, t("email.required"))
        .email(t("email.invalid"))
        .max(254, t("email.tooLong")),

      password: z
        .string()
        .min(8, t("password.tooShort", { min: 8 }))
        .max(128, t("password.tooLong"))
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, t("password.weak")),

      simplePassword: z
        .string()
        .min(1, t("password.required"))
        .min(6, t("password.tooShort", { min: 6 })),

      url: z.string().min(1, t("url.required")).url(t("url.invalid")),

      phoneNumber: z
        .string()
        .regex(/^[\+]?[1-9][\d]{0,15}$/, t("phone.invalid"))
        .optional(),

      name: z
        .string()
        .min(1, t("name.required"))
        .max(100, t("name.tooLong"))
        .regex(/^[a-zA-Z\s\-'\.]+$/, t("name.invalidChars")),

      businessName: z
        .string()
        .min(1, t("business.nameRequired"))
        .max(200, t("business.nameTooLong")),

      description: z
        .string()
        .max(1000, t("business.descriptionTooLong"))
        .optional(),
    };

    // Auth schemas
    const loginSchema = z.object({
      email: commonValidations.email,
      password: commonValidations.simplePassword,
    });

    const registerSchema = z
      .object({
        email: commonValidations.email,
        password: commonValidations.password,
        confirmPassword: z.string().min(1, t("password.required")),
      })
      .refine(data => data.password === data.confirmPassword, {
        message: t("password.mismatch"),
        path: ["confirmPassword"],
      });

    // Business schemas
    const googleMapsUrlSchema = z.object({
      url: commonValidations.url.refine(
        url =>
          url.includes("maps.google") ||
          url.includes("google.com/maps") ||
          url.includes("maps.app.goo.gl"),
        t("url.googleMapsInvalid")
      ),
    });

    const businessSchema = z.object({
      name: commonValidations.businessName,
      description: commonValidations.description,
      address: z
        .string()
        .min(1, t("business.addressRequired"))
        .max(500, t("business.addressTooLong")),
      phone: commonValidations.phoneNumber,
      website: commonValidations.url.optional(),
      category: z.string().min(1, t("business.categoryRequired")),
    });

    const placeIdSchema = z.object({
      placeId: z
        .string()
        .min(1, t("placeId.required"))
        .regex(/^[A-Za-z0-9_-]+$/, t("placeId.invalid")),
    });

    // Profile schemas
    const profileSchema = z.object({
      firstName: commonValidations.name,
      lastName: commonValidations.name,
      email: commonValidations.email,
      phone: commonValidations.phoneNumber,
      company: z.string().max(200, t("profile.companyTooLong")).optional(),
      jobTitle: z.string().max(100, t("profile.jobTitleTooLong")).optional(),
    });

    const changePasswordSchema = z
      .object({
        currentPassword: commonValidations.simplePassword,
        newPassword: commonValidations.password,
        confirmPassword: z.string().min(1, t("password.required")),
      })
      .refine(data => data.newPassword === data.confirmPassword, {
        message: t("password.mismatch"),
        path: ["confirmPassword"],
      });

    return {
      loginSchema,
      registerSchema,
      googleMapsUrlSchema,
      businessSchema,
      placeIdSchema,
      profileSchema,
      changePasswordSchema,
    };
  }, [t]);

  const value: ValidationContextType = {
    schemas,
  };

  return (
    <ValidationContext.Provider value={value}>
      {children}
    </ValidationContext.Provider>
  );
}

export function useValidationSchemas() {
  const context = useContext(ValidationContext);
  if (context === undefined) {
    throw new Error(
      "useValidationSchemas must be used within a ValidationProvider"
    );
  }
  return context.schemas;
}

// Type exports for the translated schemas
export type LoginFormData = {
  email: string;
  password: string;
};

export type RegisterFormData = {
  email: string;
  password: string;
  confirmPassword: string;
};

export type GoogleMapsUrlFormData = {
  url: string;
};

export type BusinessFormData = {
  name: string;
  description?: string;
  address: string;
  phone?: string;
  website?: string;
  category: string;
};

export type PlaceIdFormData = {
  placeId: string;
};

export type ProfileFormData = {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  company?: string;
  jobTitle?: string;
};

export type ChangePasswordFormData = {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
};
