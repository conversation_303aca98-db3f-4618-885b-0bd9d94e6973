import { z } from "zod";
import { useValidationTranslations } from "@/lib/i18n";

/**
 * Hook to create validation schemas with translated error messages
 */
export function useValidationSchemas() {
  const t = useValidationTranslations();

  // Common validation utilities with translations
  const commonValidations = {
    email: z
      .string()
      .min(1, t("email.required"))
      .email(t("email.invalid"))
      .max(254, t("email.tooLong")),

    password: z
      .string()
      .min(8, t("password.tooShort", { min: 8 }))
      .max(128, t("password.tooLong"))
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, t("password.weak")),

    simplePassword: z
      .string()
      .min(1, t("password.required"))
      .min(6, t("password.tooShort", { min: 6 })),

    url: z.string().min(1, t("url.required")).url(t("url.invalid")),

    phoneNumber: z
      .string()
      .regex(/^[\+]?[1-9][\d]{0,15}$/, t("phone.invalid"))
      .optional(),

    name: z
      .string()
      .min(1, t("name.required"))
      .max(100, t("name.tooLong"))
      .regex(/^[a-zA-Z\s\-'\.]+$/, t("name.invalidChars")),

    businessName: z
      .string()
      .min(1, t("business.nameRequired"))
      .max(200, t("business.nameTooLong")),

    description: z
      .string()
      .max(1000, t("business.descriptionTooLong"))
      .optional(),
  };

  // Auth schemas with translations
  const loginSchema = z.object({
    email: commonValidations.email,
    password: commonValidations.simplePassword,
  });

  const registerSchema = z
    .object({
      email: commonValidations.email,
      password: commonValidations.password,
      confirmPassword: z.string().min(1, t("password.required")),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t("password.mismatch"),
      path: ["confirmPassword"],
    });

  const forgotPasswordSchema = z.object({
    email: commonValidations.email,
  });

  const resetPasswordSchema = z
    .object({
      password: commonValidations.password,
      confirmPassword: z.string().min(1, t("password.required")),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t("password.mismatch"),
      path: ["confirmPassword"],
    });

  // Business schemas with translations
  const googleMapsUrlSchema = z.object({
    url: commonValidations.url.refine(
      url =>
        url.includes("maps.google") ||
        url.includes("google.com/maps") ||
        url.includes("maps.app.goo.gl"),
      t("url.googleMapsInvalid")
    ),
  });

  const businessSchema = z.object({
    name: commonValidations.businessName,
    description: commonValidations.description,
    address: z
      .string()
      .min(1, t("business.addressRequired"))
      .max(500, t("business.addressTooLong")),
    phone: commonValidations.phoneNumber,
    website: commonValidations.url.optional(),
    category: z.string().min(1, t("business.categoryRequired")),
  });

  const placeIdSchema = z.object({
    placeId: z
      .string()
      .min(1, t("placeId.required"))
      .regex(/^[A-Za-z0-9_-]+$/, t("placeId.invalid")),
  });

  // Profile schemas with translations
  const profileSchema = z.object({
    firstName: commonValidations.name,
    lastName: commonValidations.name,
    email: commonValidations.email,
    phone: commonValidations.phoneNumber,
    company: z.string().max(200, t("profile.companyTooLong")).optional(),
    jobTitle: z.string().max(100, t("profile.jobTitleTooLong")).optional(),
  });

  const changePasswordSchema = z
    .object({
      currentPassword: commonValidations.simplePassword,
      newPassword: commonValidations.password,
      confirmPassword: z.string().min(1, t("password.required")),
    })
    .refine(data => data.newPassword === data.confirmPassword, {
      message: t("password.mismatch"),
      path: ["confirmPassword"],
    });

  return {
    commonValidations,
    loginSchema,
    registerSchema,
    forgotPasswordSchema,
    resetPasswordSchema,
    googleMapsUrlSchema,
    businessSchema,
    placeIdSchema,
    profileSchema,
    changePasswordSchema,
  };
}

/**
 * Server-side validation schemas (without hooks)
 * These use the original validation messages for server-side validation
 */
export const serverValidationSchemas = {
  // Common validations
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(254, "Email is too long"),

  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password is too long")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    ),

  simplePassword: z
    .string()
    .min(1, "Password is required")
    .min(6, "Password must be at least 6 characters"),

  url: z.string().min(1, "URL is required").url("Please enter a valid URL"),

  phoneNumber: z
    .string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number")
    .optional(),

  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name is too long")
    .regex(/^[a-zA-Z\s\-'\.]+$/, "Name contains invalid characters"),

  businessName: z
    .string()
    .min(1, "Business name is required")
    .max(200, "Business name is too long"),

  description: z.string().max(1000, "Description is too long").optional(),
};

// Server-side schemas
export const serverLoginSchema = z.object({
  email: serverValidationSchemas.email,
  password: serverValidationSchemas.simplePassword,
});

export const serverRegisterSchema = z
  .object({
    email: serverValidationSchemas.email,
    password: serverValidationSchemas.password,
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export const serverGoogleMapsUrlSchema = z.object({
  url: serverValidationSchemas.url.refine(
    url =>
      url.includes("maps.google") ||
      url.includes("google.com/maps") ||
      url.includes("maps.app.goo.gl"),
    "Please enter a valid Google Maps URL"
  ),
});

// Type exports
export type LoginFormData = z.infer<typeof serverLoginSchema>;
export type RegisterFormData = z.infer<typeof serverRegisterSchema>;
export type GoogleMapsUrlFormData = z.infer<typeof serverGoogleMapsUrlSchema>;
