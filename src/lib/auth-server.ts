// Server-side authentication utilities for API routes
import { createClient } from '@supabase/supabase-js';
import { NextRequest } from 'next/server';

// Server-side authentication function for API routes
export async function authenticateUser(request: NextRequest) {
  // Get Authorization header
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { user: null, supabaseAdmin: null };
  }

  const token = authHeader.substring(7);
  
  // Create Supabase client with service role for token verification
  const supabaseAdmin = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );

  const { data: { user }, error } = await supabaseAdmin.auth.getUser(token);
  
  if (error || !user) {
    return { user: null, supabaseAdmin: null };
  }

  return { user, supabaseAdmin };
}

// Helper function to create AuthUser object from Supabase user
export function createAuthUser(user: any) {
  return {
    id: user.id,
    email: user.email!,
    aud: user.aud,
    role: user.role,
  };
}
