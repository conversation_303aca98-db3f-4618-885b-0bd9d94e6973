// Utility functions to get the correct Supabase client
// This prevents multiple client instances and provides clear usage patterns

import { supabase, supabaseServer } from './supabase';

/**
 * Get the client-side Supabase instance
 * Use this in:
 * - React components
 * - Client-side hooks  
 * - Browser-only operations
 */
export function getSupabaseClient() {
  return supabase;
}

/**
 * Get the server-side Supabase instance
 * Use this in:
 * - API routes
 * - Server actions
 * - Middleware (for read operations)
 */
export function getSupabaseServerClient() {
  return supabaseServer;
}

/**
 * Check if we're running on the server
 */
export function isServer() {
  return typeof window === 'undefined';
}

/**
 * Get the appropriate Supabase client based on environment
 * This is a convenience function, but prefer using the specific getters above
 */
export function getSupabase() {
  return isServer() ? supabaseServer : supabase;
}