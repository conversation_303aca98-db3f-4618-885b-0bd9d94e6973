import { createServerSupabaseClient } from './services/database';
import { AuthUser } from '@/types';
import { NextRequest } from 'next/server';

/**
 * Get the current user from the request headers
 * This function extracts the JWT token from the Authorization header
 * and validates it with Supabase
 */
export async function getCurrentUser(request: NextRequest): Promise<AuthUser | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    const supabase = createServerSupabaseClient();

    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      console.error('Auth error:', error);
      return null;
    }

    return {
      id: user.id,
      email: user.email!,
      aud: user.aud,
      role: user.role,
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Middleware function to protect API routes
 * Returns the authenticated user or throws an error
 */
export async function requireAuth(request: NextRequest): Promise<AuthUser> {
  const user = await getCurrentUser(request);
  
  if (!user) {
    throw new Error('Authentication required');
  }

  return user;
}

/**
 * Extract user from Supabase session (for client-side)
 */
export function extractUserFromSession(session: any): AuthUser | null {
  if (!session?.user) {
    return null;
  }

  return {
    id: session.user.id,
    email: session.user.email,
    aud: session.user.aud,
    role: session.user.role,
  };
}

/**
 * Validate JWT token format
 */
export function isValidJWT(token: string): boolean {
  try {
    const parts = token.split('.');
    return parts.length === 3;
  } catch {
    return false;
  }
}

/**
 * Create authorization header for API requests
 */
export function createAuthHeader(token: string): Record<string, string> {
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
}

/**
 * Check if user has required permissions
 */
export function hasPermission(user: AuthUser, requiredRole?: string): boolean {
  if (!requiredRole) {
    return true; // No specific role required
  }

  return user.role === requiredRole || user.role === 'admin';
}

/**
 * Generate a secure session token (for additional security if needed)
 */
export function generateSessionToken(): string {
  return crypto.randomUUID();
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
export function isValidPassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
