import { useLanguage } from "@/providers/LanguageProvider";
import { type Locale } from "@/i18n/config";

/**
 * Translation hook for common/shared text
 */
export function useCommonTranslations() {
  const { t } = useLanguage();
  return (key: string, params?: Record<string, string | number>) =>
    t(`common.${key}`, params);
}

/**
 * Translation hook for form-related text
 */
export function useFormTranslations() {
  const { t } = useLanguage();
  return (key: string, params?: Record<string, string | number>) =>
    t(`forms.${key}`, params);
}

/**
 * Translation hook for validation messages
 */
export function useValidationTranslations() {
  const { t } = useLanguage();
  return (key: string, params?: Record<string, string | number>) =>
    t(`validation.${key}`, params);
}

/**
 * Translation hook for page-specific text
 */
export function usePageTranslations(page: string) {
  const { t } = useLanguage();
  return (key: string, params?: Record<string, string | number>) =>
    t(`pages.${page}.${key}`, params);
}

/**
 * Translation hook for component-specific text
 */
export function useComponentTranslations(component: string) {
  const { t } = useLanguage();
  return (key: string, params?: Record<string, string | number>) =>
    t(`components.${component}.${key}`, params);
}

/**
 * Translation hook for dashboard
 */
export function useDashboardTranslations() {
  const { t } = useLanguage();
  return (key: string, params?: Record<string, string | number>) =>
    t(`pages.dashboard.${key}`, params);
}

/**
 * Translation hook for authentication pages
 */
export function useAuthTranslations() {
  const { t } = useLanguage();
  return (key: string, params?: Record<string, string | number>) =>
    t(`pages.auth.${key}`, params);
}

/**
 * Translation hook for business-related text
 */
export function useBusinessTranslations() {
  const { t } = useLanguage();
  return (key: string, params?: Record<string, string | number>) =>
    t(`components.business.${key}`, params);
}

/**
 * Utility to get translated validation messages for Zod schemas
 */
export function useValidationMessages() {
  const t = useValidationTranslations();

  return {
    // Email validations
    emailRequired: () => t("email.required"),
    emailInvalid: () => t("email.invalid"),
    emailTooLong: () => t("email.tooLong"),

    // Password validations
    passwordRequired: () => t("password.required"),
    passwordTooShort: (min: number) => t("password.tooShort", { min }),
    passwordTooLong: () => t("password.tooLong"),
    passwordWeak: () => t("password.weak"),
    passwordMismatch: () => t("password.mismatch"),

    // General validations
    required: (field: string) => t("general.required", { field }),
    tooShort: (field: string, min: number) =>
      t("general.tooShort", { field, min }),
    tooLong: (field: string) => t("general.tooLong", { field }),
    invalid: (field: string) => t("general.invalid", { field }),
    invalidFormat: (field: string) => t("general.invalidFormat", { field }),

    // URL validations
    urlRequired: () => t("url.required"),
    urlInvalid: () => t("url.invalid"),
    googleMapsUrlInvalid: () => t("url.googleMapsInvalid"),

    // Phone validations
    phoneInvalid: () => t("phone.invalid"),

    // Name validations
    nameRequired: () => t("name.required"),
    nameTooLong: () => t("name.tooLong"),
    nameInvalidChars: () => t("name.invalidChars"),

    // Business validations
    businessNameRequired: () => t("business.nameRequired"),
    businessNameTooLong: () => t("business.nameTooLong"),
    addressRequired: () => t("business.addressRequired"),
    addressTooLong: () => t("business.addressTooLong"),
    categoryRequired: () => t("business.categoryRequired"),
    descriptionTooLong: () => t("business.descriptionTooLong"),

    // Profile validations
    companyTooLong: () => t("profile.companyTooLong"),
    jobTitleTooLong: () => t("profile.jobTitleTooLong"),

    // Place ID validations
    placeIdRequired: () => t("placeId.required"),
    placeIdInvalid: () => t("placeId.invalid"),

    // Terms validation
    termsRequired: () => t("terms.required"),
  };
}

/**
 * Utility to format dates according to locale
 */
export function formatDate(date: string | Date, locale: Locale): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  const localeMap: Record<Locale, string> = {
    en: "en-US",
    tr: "tr-TR",
    nl: "nl-NL",
  };

  return dateObj.toLocaleDateString(localeMap[locale], {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

/**
 * Utility to format numbers according to locale
 */
export function formatNumber(number: number, locale: Locale): string {
  const localeMap: Record<Locale, string> = {
    en: "en-US",
    tr: "tr-TR",
    nl: "nl-NL",
  };

  return number.toLocaleString(localeMap[locale]);
}

/**
 * Translation keys type for better TypeScript support
 */
export type TranslationKey =
  | `common.${string}`
  | `forms.${string}`
  | `validation.${string}`
  | `pages.${string}`
  | `components.${string}`;
