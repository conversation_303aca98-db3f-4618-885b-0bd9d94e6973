import { type Locale, locales, defaultLocale } from '@/i18n/config';

/**
 * Get the current language from browser cookies
 * This can be used in both client and server contexts
 */
export function getCurrentLanguageFromCookie(): Locale {
  if (typeof document === 'undefined') {
    // Server-side: can't access document
    return defaultLocale;
  }

  const savedLocale = document.cookie
    .split('; ')
    .find(row => row.startsWith('locale='))
    ?.split('=')[1] as Locale;

  if (savedLocale && locales.includes(savedLocale)) {
    return savedLocale;
  }

  return defaultLocale;
}

/**
 * Get the current language from the URL parameter (for Next.js dynamic routes)
 * This can be used in server components
 */
export function getCurrentLanguageFromHeaders(headers: Headers): Locale {
  const acceptLanguage = headers.get('accept-language');
  
  if (acceptLanguage) {
    // Parse Accept-Language header and find first supported locale
    const preferredLanguages = acceptLanguage
      .split(',')
      .map(lang => lang.split(';')[0]?.trim().split('-')[0])
      .filter((lang): lang is string => !!lang);

    for (const lang of preferredLanguages) {
      if (locales.includes(lang as Locale)) {
        return lang as Locale;
      }
    }
  }

  return defaultLocale;
}

/**
 * Convert locale to language codes used by external APIs
 */
export function mapLocaleToApiLanguage(locale: Locale): string {
  const languageMap: Record<Locale, string> = {
    en: 'en',
    tr: 'tr', 
    nl: 'nl',
  };

  return languageMap[locale] || 'en';
}

/**
 * Get user's preferred language for API calls
 * This should be used in React components that make API calls
 */
export function getUserLanguageForApi(): Locale {
  return getCurrentLanguageFromCookie();
}