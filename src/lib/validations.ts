import { z } from "zod";

/**
 * Common validation utilities
 */
export const commonValidations = {
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(254, "Email is too long"),

  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password is too long")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    ),

  simplePassword: z
    .string()
    .min(1, "Password is required")
    .min(6, "Password must be at least 6 characters"),

  url: z.string().min(1, "URL is required").url("Please enter a valid URL"),

  phoneNumber: z
    .string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number")
    .optional(),

  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name is too long")
    .regex(/^[a-zA-Z\s\-'\.]+$/, "Name contains invalid characters"),

  businessName: z
    .string()
    .min(1, "Business name is required")
    .max(200, "Business name is too long"),

  description: z.string().max(1000, "Description is too long").optional(),
};

/**
 * Auth schemas
 */
export const loginSchema = z.object({
  email: commonValidations.email,
  password: commonValidations.simplePassword,
});

export const registerSchema = z
  .object({
    email: commonValidations.email,
    password: commonValidations.password,
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export const forgotPasswordSchema = z.object({
  email: commonValidations.email,
});

export const resetPasswordSchema = z
  .object({
    password: commonValidations.password,
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

/**
 * Business schemas
 */
export const googleMapsUrlSchema = z.object({
  url: commonValidations.url.refine(
    url =>
      url.includes("maps.google") ||
      url.includes("google.com/maps") ||
      url.includes("maps.app.goo.gl"),
    "Please enter a valid Google Maps URL"
  ),
});

export const businessSchema = z.object({
  name: commonValidations.businessName,
  description: commonValidations.description,
  address: z
    .string()
    .min(1, "Address is required")
    .max(500, "Address is too long"),
  phone: commonValidations.phoneNumber,
  website: commonValidations.url.optional(),
  category: z.string().min(1, "Category is required"),
});

export const placeIdSchema = z.object({
  placeId: z
    .string()
    .min(1, "Place ID is required")
    .regex(/^[A-Za-z0-9_-]+$/, "Invalid Place ID format"),
});

/**
 * Profile schemas
 */
export const profileSchema = z.object({
  firstName: commonValidations.name,
  lastName: commonValidations.name,
  email: commonValidations.email,
  phone: commonValidations.phoneNumber,
  company: z.string().max(200, "Company name is too long").optional(),
  jobTitle: z.string().max(100, "Job title is too long").optional(),
});

export const changePasswordSchema = z
  .object({
    currentPassword: commonValidations.simplePassword,
    newPassword: commonValidations.password,
    confirmPassword: z.string().min(1, "Please confirm your new password"),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

/**
 * Type exports for use in components
 */
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type GoogleMapsUrlFormData = z.infer<typeof googleMapsUrlSchema>;
export type BusinessFormData = z.infer<typeof businessSchema>;
export type PlaceIdFormData = z.infer<typeof placeIdSchema>;
export type ProfileFormData = z.infer<typeof profileSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
