import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { anthropic } from '@ai-sdk/anthropic';
import { Review, Summary, AuthUser } from '@/types';
import { DatabaseService } from './database';

export interface SummaryRequest {
  reviews: Review[];
  periodStart: string;
  periodEnd: string;
  businessName?: string;
}

export interface SummaryResult {
  positive_themes: string[];
  negative_themes: string[];
  recommended_improvements: string[];
  total_reviews: number;
  sentiment_distribution: Record<string, number>;
  key_insights: string[];
  overall_rating_trend: string;
}

export class SummarizationService {
  private aiModel: any;
  private databaseService: DatabaseService;

  constructor(config: {
    provider: 'openai' | 'google' | 'anthropic';
    apiKey: string;
    model?: string;
  }) {
    // Configure AI model based on provider
    switch (config.provider) {
      case 'openai':
        this.aiModel = openai(config.model || 'gpt-3.5-turbo');
        break;
      case 'google':
        this.aiModel = google(config.model || 'gemini-pro');
        break;
      case 'anthropic':
        this.aiModel = anthropic(config.model || 'claude-3-haiku-20240307');
        break;
      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }

    this.databaseService = new DatabaseService(true); // Use service role
  }

  /**
   * Generate AI summary from reviews
   */
  async generateSummary(request: SummaryRequest): Promise<SummaryResult> {
    try {
      console.log(`Generating summary for ${request.reviews.length} reviews`);

      if (request.reviews.length === 0) {
        return {
          positive_themes: [],
          negative_themes: [],
          recommended_improvements: ['No reviews available for analysis'],
          total_reviews: 0,
          sentiment_distribution: { positive: 0, negative: 0, neutral: 0 },
          key_insights: ['No reviews to analyze'],
          overall_rating_trend: 'No data available',
        };
      }

      // Prepare review data for analysis
      const reviewTexts = request.reviews.map(review => ({
        text: review.review_text,
        rating: review.rating,
        sentiment: review.sentiment,
        date: review.review_date,
        author: review.author_name,
      }));

      // Calculate sentiment distribution
      const sentimentDistribution = this.calculateSentimentDistribution(request.reviews);

      // Generate the prompt for AI analysis
      const prompt = this.createSummaryPrompt(reviewTexts, request.businessName);

      const { text: responseText } = await generateText({
        model: this.aiModel,
        prompt,
        temperature: 0.3,
      });

      try {
        const parsed = JSON.parse(responseText.trim());

        return {
          positive_themes: parsed.positive_themes || [],
          negative_themes: parsed.negative_themes || [],
          recommended_improvements: parsed.recommended_improvements || [],
          total_reviews: request.reviews.length,
          sentiment_distribution: sentimentDistribution,
          key_insights: parsed.key_insights || [],
          overall_rating_trend: parsed.overall_rating_trend || 'Stable',
        };
      } catch (parseError) {
        console.error('Error parsing AI response:', parseError);
        console.error('Raw response:', responseText);

        // Fallback to basic analysis
        return this.generateBasicSummary(request.reviews, sentimentDistribution);
      }
    } catch (error) {
      console.error('Error generating summary:', error);
      throw new Error(`Failed to generate summary: ${error}`);
    }
  }

  /**
   * Save summary to database
   */
  async saveSummary(
    user: AuthUser,
    summaryResult: SummaryResult,
    periodStart: string,
    periodEnd: string
  ): Promise<Summary> {
    try {
      return await this.databaseService.createSummary({
        user_id: user.id,
        period_start: periodStart,
        period_end: periodEnd,
        positive_themes: summaryResult.positive_themes,
        negative_themes: summaryResult.negative_themes,
        recommended_improvements: summaryResult.recommended_improvements,
        total_reviews: summaryResult.total_reviews,
        sentiment_distribution: summaryResult.sentiment_distribution,
      });
    } catch (error) {
      console.error('Error saving summary:', error);
      throw new Error(`Failed to save summary: ${error}`);
    }
  }

  /**
   * Get user summaries
   */
  async getUserSummaries(user: AuthUser): Promise<Summary[]> {
    try {
      return await this.databaseService.getUserSummaries(user.id);
    } catch (error) {
      console.error('Error getting user summaries:', error);
      throw new Error(`Failed to get summaries: ${error}`);
    }
  }

  /**
   * Create the prompt for AI summarization
   */
  private createSummaryPrompt(
    reviews: Array<{
      text: string;
      rating: number;
      sentiment?: string;
      date: string;
      author?: string;
    }>,
    businessName?: string
  ): string {
    const businessContext = businessName ? ` for ${businessName}` : '';
    
    return `
Analyze the following customer reviews${businessContext} and provide a comprehensive summary in JSON format.

Reviews data:
${reviews.map((review, index) => `
Review ${index + 1}:
- Rating: ${review.rating}/5 stars
- Sentiment: ${review.sentiment || 'unknown'}
- Date: ${review.date}
- Author: ${review.author || 'Anonymous'}
- Text: "${review.text}"
`).join('\n')}

Please analyze these reviews and respond with ONLY a JSON object in this exact format:
{
  "positive_themes": ["theme1", "theme2", "theme3"],
  "negative_themes": ["issue1", "issue2", "issue3"],
  "recommended_improvements": ["improvement1", "improvement2", "improvement3"],
  "key_insights": ["insight1", "insight2", "insight3"],
  "overall_rating_trend": "Improving" | "Declining" | "Stable"
}

Guidelines:
- Extract 3-5 main positive themes that customers frequently mention
- Identify 3-5 main negative themes or complaints
- Provide 3-5 specific, actionable improvement recommendations
- Include 3-5 key insights about customer behavior or preferences
- Determine if the overall rating trend is improving, declining, or stable based on recent reviews
- Keep each item concise (1-2 sentences max)
- Focus on actionable insights that can help improve the business

Respond with ONLY the JSON object, no other text.
`;
  }

  /**
   * Calculate sentiment distribution from reviews
   */
  private calculateSentimentDistribution(reviews: Review[]): Record<string, number> {
    const distribution = {
      positive: 0,
      negative: 0,
      neutral: 0,
    };

    reviews.forEach(review => {
      const sentiment = review.sentiment || 'neutral';
      distribution[sentiment]++;
    });

    return distribution;
  }

  /**
   * Generate basic summary as fallback
   */
  private generateBasicSummary(
    reviews: Review[],
    sentimentDistribution: Record<string, number>
  ): SummaryResult {
    const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
    
    // Basic analysis based on ratings and common patterns
    const positiveThemes: string[] = [];
    const negativeThemes: string[] = [];
    const recommendations: string[] = [];

    if (averageRating >= 4) {
      positiveThemes.push('High customer satisfaction');
      positiveThemes.push('Quality service delivery');
    } else if (averageRating >= 3) {
      positiveThemes.push('Moderate customer satisfaction');
      recommendations.push('Focus on improving service quality');
    } else {
      negativeThemes.push('Below average customer satisfaction');
      recommendations.push('Urgent attention needed to improve service quality');
    }

    // Analyze rating distribution
    const highRatings = reviews.filter(r => r.rating >= 4).length;
    const lowRatings = reviews.filter(r => r.rating <= 2).length;

    if (highRatings > lowRatings * 2) {
      positiveThemes.push('Consistently positive customer experiences');
    } else if (lowRatings > highRatings) {
      negativeThemes.push('Frequent negative customer experiences');
      recommendations.push('Investigate and address common customer complaints');
    }

    // Add generic recommendations
    if (recommendations.length === 0) {
      recommendations.push('Continue monitoring customer feedback');
      recommendations.push('Maintain current service standards');
    }

    return {
      positive_themes: positiveThemes,
      negative_themes: negativeThemes,
      recommended_improvements: recommendations,
      total_reviews: reviews.length,
      sentiment_distribution: sentimentDistribution,
      key_insights: [
        `Average rating: ${averageRating.toFixed(1)}/5`,
        `${sentimentDistribution.positive} positive reviews`,
        `${sentimentDistribution.negative} negative reviews`,
      ],
      overall_rating_trend: 'Stable',
    };
  }

  /**
   * Generate weekly summary for a business
   */
  async generateWeeklySummary(
    user: AuthUser,
    placeId: string,
    businessName?: string
  ): Promise<Summary> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    const { reviews } = await this.databaseService.getReviewsForPlace(
      user.id,
      placeId,
      {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        includeAllReviews: true,
      }
    );

    const summaryResult = await this.generateSummary({
      reviews,
      periodStart: startDate.toISOString(),
      periodEnd: endDate.toISOString(),
      businessName,
    });

    return await this.saveSummary(
      user,
      summaryResult,
      startDate.toISOString(),
      endDate.toISOString()
    );
  }

  /**
   * Generate monthly summary for a business
   */
  async generateMonthlySummary(
    user: AuthUser,
    placeId: string,
    businessName?: string
  ): Promise<Summary> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);

    const { reviews } = await this.databaseService.getReviewsForPlace(
      user.id,
      placeId,
      {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        includeAllReviews: true,
      }
    );

    const summaryResult = await this.generateSummary({
      reviews,
      periodStart: startDate.toISOString(),
      periodEnd: endDate.toISOString(),
      businessName,
    });

    return await this.saveSummary(
      user,
      summaryResult,
      startDate.toISOString(),
      endDate.toISOString()
    );
  }
}
