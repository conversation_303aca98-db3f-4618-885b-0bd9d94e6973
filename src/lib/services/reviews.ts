import { ApifyService } from './apify';
import { DatabaseService } from './database';
import { Review } from '@/types/supabase';
import { AuthUser} from '@/types';
import { PaginatedReviewsResponse } from '@/types/api';

export class ReviewService {
  private apifyService: ApifyService;
  private databaseService: DatabaseService;

  constructor(apifyApiToken: string) {
    this.apifyService = new ApifyService(apifyApiToken);
    this.databaseService = new DatabaseService(true); // Use service role for server operations
  }

  /**
   * Fetch reviews from Apify and store them in the database
   */
  async fetchReviews(
    placeId: string,
    user: AuthUser,
    businessName?: string,
    rateLimitSeconds = 0
  ): Promise<{
    message: string;
    business_name: string;
    business_id: string;
    place_id: string;
    total_reviews: number;
    new_reviews: number;
    existing_reviews: number;
    last_fetch_time: string;
  }> {
    try {
      console.log(`Received request for place_id: ${placeId}`);
      console.log(`User: ${user.email}`);

      // Check rate limiting
      const rateLimitCheck = await this.databaseService.checkRateLimit(placeId, rateLimitSeconds);
      if (!rateLimitCheck.canFetch) {
        throw new Error(rateLimitCheck.message);
      }

      let businessName_final = businessName || 'Unknown Business';
      let businessAddress: string | undefined;
      // Check if business exists in our database
      let business = await this.databaseService.getBusinessByPlaceId(placeId);
      let businessId: string;

      if (business) {
        // Business exists, update info
        businessId = business.id;
        console.log(`Found existing business with ID: ${businessId}`);

        business = await this.databaseService.updateBusiness(businessId, {
          business_name: businessName_final,
          business_address: businessAddress,
        });
      } else {
        // Create new business record
        console.log(`Creating new business: ${businessName_final}`);
        business = await this.databaseService.createBusiness({
          google_place_id: placeId,
          business_name: businessName_final,
          business_address: businessAddress,
        });
        businessId = business.id;
        console.log(`Created new business with ID: ${businessId}`);
      }

      // Check if user already has this business in their profile
      const existingProfile = await this.databaseService.getBusinessProfile(user.id, businessId);
      if (!existingProfile) {
        await this.databaseService.createBusinessProfile(user.id, businessId);
        console.log('Created business profile for user');
      } else {
        console.log('User already has this business in their profile');
      }

      // Fetch reviews from Apify
      const apifyReviews = await this.apifyService.fetchReviews(placeId);
      console.log(`Fetched ${apifyReviews.length} reviews from Apify`);

      console.log('Processing reviews for storage');
      const storedReviews: Review[] = [];
      let newReviewsCount = 0;

      for (const reviewData of apifyReviews) {
        // Check for existing review by Google review ID
        const existingById = await this.databaseService.getReviewByGoogleId(reviewData.reviewId);

        if (existingById) {
          // Review already exists
          storedReviews.push(existingById);
          console.log(`Review already exists (by ID): ${reviewData.authorName} - ${reviewData.rating} stars`);
        } else {
          // Store new review
          try {
            const newReview = await this.databaseService.createReview({
              user_id: user.id,
              google_place_id: placeId,
              review_text: reviewData.text,
              rating: reviewData.rating,
              author_name: reviewData.authorName || null,
              author_avatar: reviewData.authorAvatar || null,
              author_url: reviewData.authorUrl || null,
              author_id: reviewData.authorId || null,
              review_date: reviewData.publishedAtDate && reviewData.publishedAtDate.trim() !== '' ? reviewData.publishedAtDate : new Date().toISOString(),
              updated_review_date: reviewData.updatedAtDate && reviewData.updatedAtDate.trim() !== '' && reviewData.updatedAtDate !== reviewData.publishedAtDate ? reviewData.updatedAtDate : null,
              google_review_id: reviewData.reviewId,
              photo_urls: reviewData.photos || null,
              language: reviewData.language || null,
              original_language: reviewData.language || null,
              translated: false,
              likes: reviewData.likes || 0,
              reply: reviewData.reply || null,
            });

            storedReviews.push(newReview);
            newReviewsCount++;
            console.log(`Stored NEW review: ${reviewData.authorName} - ${reviewData.rating} stars`);
          } catch (error) {
            // Handle potential race condition where review was inserted by another request
            if (error instanceof Error && error.message.includes('duplicate key')) {
              console.log(`Review was just inserted by another request, fetching existing: ${reviewData.authorName}`);
              const existingRetry = await this.databaseService.getReviewByGoogleId(reviewData.reviewId);
              if (existingRetry) {
                storedReviews.push(existingRetry);
              }
            } else {
              throw error;
            }
          }
        }
      }

      // Update last fetch time
      await this.databaseService.updateLastFetchTime(placeId);

      return {
        message: `Successfully processed ${apifyReviews.length} reviews from Apify`,
        business_name: businessName_final,
        business_id: businessId,
        place_id: placeId,
        total_reviews: storedReviews.length,
        new_reviews: newReviewsCount,
        existing_reviews: storedReviews.length - newReviewsCount,
        last_fetch_time: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error occurred:', error);
      throw new Error(`Error fetching reviews: ${error}`);
    }
  }

  /**
   * Get reviews with pagination and filters
   */
  async getReviewsPaginated(
    user: AuthUser,
    options: {
      page?: number;
      limit?: number;
      google_place_id?: string;
      start_date?: string;
      end_date?: string;
      sentiment?: string;
      include_all_reviews?: boolean;
    } = {}
  ): Promise<PaginatedReviewsResponse> {
    try {
      const { page = 1, limit = 10 } = options;
      
      const { reviews, total } = await this.databaseService.getReviewsForPlace(
        user.id,
        options.google_place_id || '',
        {
          page,
          limit,
          startDate: options.start_date,
          endDate: options.end_date,
          sentiment: options.sentiment,
          includeAllReviews: options.include_all_reviews,
        }
      );

      const totalPages = Math.ceil(total / limit);
      const averageRating = reviews.length > 0 
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
        : undefined;

      return {
        reviews,
        total,
        page,
        limit,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_previous: page > 1,
        average_rating: averageRating,
      };
    } catch (error) {
      console.error('Error getting paginated reviews:', error);
      throw new Error(`Error fetching reviews: ${error}`);
    }
  }

  /**
   * Get reviews for a specific place
   */
  async getReviewsForPlace(
    user: AuthUser,
    placeId: string,
    includeAllReviews = false
  ): Promise<{
    reviews: Review[];
    stats: {
      total: number;
      averageRating: number;
      sentimentDistribution: Record<string, number>;
    };
  }> {
    try {
      const { reviews } = await this.databaseService.getReviewsForPlace(
        user.id,
        placeId,
        { includeAllReviews }
      );

      const stats = await this.databaseService.getReviewStats(
        user.id,
        placeId,
        undefined,
        undefined,
        includeAllReviews
      );

      return { reviews, stats };
    } catch (error) {
      console.error('Error getting reviews for place:', error);
      throw new Error(`Error fetching reviews: ${error}`);
    }
  }

  /**
   * Get review statistics
   */
  async getReviewStats(
    user: AuthUser,
    options: {
      google_place_id?: string;
      start_date?: string;
      end_date?: string;
      include_all_reviews?: boolean;
    } = {}
  ): Promise<{
    total: number;
    averageRating: number;
    sentimentDistribution: Record<string, number>;
  }> {
    try {
      return await this.databaseService.getReviewStats(
        user.id,
        options.google_place_id,
        options.start_date,
        options.end_date,
        options.include_all_reviews || false
      );
    } catch (error) {
      console.error('Error getting review stats:', error);
      throw new Error(`Error fetching review statistics: ${error}`);
    }
  }

  /**
   * Check rate limit status for a business
   */
  async getRateLimitStatus(placeId: string, rateLimitSeconds: number): Promise<{
    can_fetch: boolean;
    message: string;
    last_fetch_time?: string;
    next_available_time?: string;
  }> {
    try {
      const rateLimitCheck = await this.databaseService.checkRateLimit(placeId, rateLimitSeconds);
      return {
        can_fetch: rateLimitCheck.canFetch,
        message: rateLimitCheck.message,
        last_fetch_time: rateLimitCheck.lastFetchTime,
        next_available_time: rateLimitCheck.nextAvailableTime,
      };
    } catch (error) {
      console.error('Error checking rate limit status:', error);
      throw new Error(`Error checking rate limit: ${error}`);
    }
  }
}
