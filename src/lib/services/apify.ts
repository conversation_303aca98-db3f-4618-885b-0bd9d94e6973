import { ApifyClient } from 'apify-client';
import { ApifyBusinessInfo, ApifyReview } from '@/types';
import { type Locale } from '@/i18n/config';

// Apify response types

interface ApifyReviewItem {
  reviewId?: string;
  text?: string;
  stars?: number;
  name?: string;
  publishedAtDate?: string;
  reviewImageUrls?: string[];
  authorAvatar?: string;
  authorUrl?: string;
  authorId?: string;
  updatedAtDate?: string;
  likes?: number;
  reply?: string;
  lang?: string;
}

export class ApifyService {
  private client: ApifyClient;
  private apiToken: string;

  constructor(apiToken: string) {
    if (!apiToken || apiToken.trim() === '') {
      throw new Error('Apify API token is required');
    }
    
    console.log('Initializing Apify client with token:', apiToken.substring(0, 10) + '...');
    
    this.apiToken = apiToken;
    this.client = new ApifyClient({
      token: apiToken,
    });
  }

  /**
   * Search for multiple businesses by location and category
   */
  async searchBusinessesByLocation(params: {
    location: string;
    category?: string;
    limit?: number;
  }): Promise<ApifyBusinessInfo[]> {
    try {
      const { location, category, limit = 15 } = params;

      // Build search query
      let searchQuery = location;
      if (category) {
        searchQuery = `${category} in ${location}`;
      }

      const input = {
        searchStringsArray: [searchQuery],
        maxCrawledPlacesPerSearch: limit,
        language: 'en',
        countryCode: 'TR', // Based on the selected data showing İzmir, Turkey
        includeImages: false,
        includeReviews: false,
        includeOpeningHours: false,
        includePeopleAlsoSearch: false,
        maxReviews: 0,
        maxImages: 0,
      };

      console.log('Searching businesses with Apify:', input);

      const run = await this.client.actor('nwua9Gu5YrADL7ZDj').call(input);
      const { items } = await this.client.dataset(run.defaultDatasetId).listItems();

      if (!items || items.length === 0) {
        console.log('No businesses found');
        return [];
      }

      return items.map((item: any) => ({
        placeId: item.placeId || '',
        title: item.title || '',
        url: item.url || '',
        street: item.street || '',
        city: item.city || '',
        state: item.state || '',
        countryCode: item.countryCode || '',
        phone: item.phone || '',
        website: item.website || '',
        totalScore: item.totalScore || 0,
        reviewsCount: item.reviewsCount || 0,
        categories: item.categories || [],
      }));
    } catch (error) {
      console.error('Error searching businesses by location:', error);
      throw new Error(`Failed to search businesses: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Fetch business information using official Apify Google Maps actor via HTTP API
   */
  async fetchBusinessInfo(searchQuery: string, locationQuery: string = 'United States', language: Locale = 'en'): Promise<ApifyBusinessInfo[]> {
    try {
      const input = {
        searchStringsArray: [searchQuery],
        locationQuery: locationQuery,
        maxCrawledPlacesPerSearch: 1,
        language: language,
        searchMatching: 'all',
        placeMinimumStars: '',
        website: 'allPlaces',
        skipClosedPlaces: false,
        scrapePlaceDetailPage: false,
        scrapeTableReservationProvider: false,
        includeWebResults: false,
        scrapeDirectories: false,
        maxQuestions: 0,
        scrapeContacts: false,
        maximumLeadsEnrichmentRecords: 0,
        maxReviews: 0,
        reviewsSort: 'newest',
        reviewsFilterString: '',
        reviewsOrigin: 'all',
        scrapeReviewsPersonalData: true,
        scrapeImageAuthors: <AUTHORS>
        allPlacesNoSearchAction: ''
      };

      console.log('Calling Apify actor via HTTP API with input:', input);
      console.log('Using token:', this.apiToken.substring(0, 15) + '...');
      
      // Use direct HTTP API call instead of ApifyClient
      const response = await fetch(`https://api.apify.com/v2/acts/nwua9Gu5YrADL7ZDj/run-sync-get-dataset-items?token=${this.apiToken}&timeout=180`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const items = await response.json();

      if (!items || items.length === 0) {
        console.log('No items returned from Apify');
        return [];
      }

      return items.map((item: any) => ({
        placeId: item.placeId || '',
        title: item.title || '',
        address: item.address || '',
        phone: item.phone || '',
        website: item.website || '',
        rating: item.totalScore || 0,
        reviewsCount: item.reviewsCount || 0,
        categories: item.categories || (item.categoryName ? [item.categoryName] : []),
      }));
    } catch (error) {
      console.error('Error fetching business info from Apify:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('User was not found') || error.message.includes('authentication token') || error.message.includes('401')) {
          throw new Error('Apify authentication failed. Please check your API token.');
        }
        throw new Error(`Failed to fetch business information: ${error.message}`);
      }
      
      throw new Error(`Failed to fetch business information: ${error}`);
    }
  }

  /**
   * Fetch business information by Place ID using startUrls
   */
  async fetchBusinessByPlaceId(placeId: string): Promise<ApifyBusinessInfo | null> {
    try {
      const input = {
        startUrls: [`https://maps.google.com/maps?cid=${placeId}`],
        maxCrawledPlacesPerSearch: 1,
        language: 'en',
        searchMatching: 'all',
        placeMinimumStars: '',
        website: 'allPlaces',
        skipClosedPlaces: false,
        scrapePlaceDetailPage: false,
        scrapeTableReservationProvider: false,
        includeWebResults: false,
        scrapeDirectories: false,
        maxQuestions: 0,
        scrapeContacts: false,
        maximumLeadsEnrichmentRecords: 0,
        maxReviews: 0,
        reviewsSort: 'newest',
        reviewsFilterString: '',
        reviewsOrigin: 'all',
        scrapeReviewsPersonalData: true,
        scrapeImageAuthors: <AUTHORS>
        allPlacesNoSearchAction: ''
      };

      console.log('Calling Apify business scraper for place ID with input:', input);
      
      const run = await this.client.actor('nwua9Gu5YrADL7ZDj').call(input);
      
      if (!run.defaultDatasetId) {
        throw new Error('No dataset ID returned from Apify run');
      }
      
      const { items } = await this.client.dataset(run.defaultDatasetId).listItems();

      if (!items || items.length === 0) {
        console.log('No items returned from Apify for place ID:', placeId);
        return null;
      }

      const item = items[0] as any;
      if (!item) {
        return null;
      }

      return {
        placeId: item.placeId || '',
        title: item.title || '',
        address: item.address || '',
        phone: item.phone || '',
        website: item.website || '',
        rating: item.totalScore || 0,
        reviewsCount: item.reviewsCount || 0,
        categories: item.categories || (item.categoryName ? [item.categoryName] : []),
      };
    } catch (error) {
      console.error('Error fetching business by place ID from Apify:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('User was not found') || error.message.includes('authentication token')) {
          throw new Error('Apify authentication failed. Please check your API token.');
        }
        throw new Error(`Failed to fetch business by place ID: ${error.message}`);
      }
      
      throw new Error(`Failed to fetch business by place ID: ${error}`);
    }
  }

  /**
   * Fetch reviews using the specialized reviews scraper actor
   */
  async fetchReviews(placeId: string, maxReviews: number = 100): Promise<ApifyReview[]> {
    try {
      const input = {
        urls: [`https://maps.google.com/maps?cid=${placeId}`],
        category: 'reviews',
        keyword: '',
        lang: 'all',
        sorting: '2', // Newest first
        replies: 'all',
        source: '',
        daterange: [],
        country: 'us',
        mylatitude: '19.935915607405615',
        mylongitude: '-155.17564762636795',
        maximum: maxReviews,
        delay: 2,
        proxy: {
          useApifyProxy: true
        }
      };

      console.log('Fetching reviews with input:', input);
      
      const run = await this.client.actor('aqq1hZcxdXQPLLAi0').call(input);
      
      if (!run.defaultDatasetId) {
        throw new Error('No dataset ID returned from reviews scraper run');
      }
      
      const { items } = await this.client.dataset(run.defaultDatasetId).listItems();

      if (!items || items.length === 0) {
        console.log('No reviews returned from Apify for place ID:', placeId);
        return [];
      }

      return items.map((item: ApifyReviewItem) => ({
        reviewId: item.reviewId || `${item.name}_${Date.now()}`,
        text: item.text || '',
        rating: Math.max(1, Math.min(5, item.stars || 1)),
        authorName: item.name,
        authorAvatar: item.authorAvatar,
        authorUrl: item.authorUrl,
        authorId: item.authorId,
        publishedAtDate: item.publishedAtDate || new Date().toISOString(),
        updatedAtDate: item.updatedAtDate || undefined,
        photos: item.reviewImageUrls || [],
        language: item.lang || 'en',
        likes: item.likes,
        reply: item.reply,
      }));
    } catch (error) {
      console.error('Error fetching reviews from Apify:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('User was not found') || error.message.includes('authentication token')) {
          throw new Error('Apify authentication failed. Please check your API token.');
        }
        throw new Error(`Failed to fetch reviews: ${error.message}`);
      }
      
      throw new Error(`Failed to fetch reviews: ${error}`);
    }
  }

  /**
   * Parse Google Maps URL to extract place information
   */
  parseGoogleMapsUrl(url: string): { placeId?: string; query?: string } | null {
    try {
      const urlObj = new URL(url);
      
      // Check for place_id in URL
      const placeIdMatch = url.match(/place_id:([A-Za-z0-9_-]+)/);
      if (placeIdMatch) {
        return { placeId: placeIdMatch[1] };
      }

      // Check for place_id in query parameters
      const placeId = urlObj.searchParams.get('place_id');
      if (placeId) {
        return { placeId };
      }

      // Check for cid (Customer ID) parameter which is also a valid place identifier
      const cid = urlObj.searchParams.get('cid');
      if (cid) {
        return { placeId: cid };
      }

      // Extract search query from various URL formats
      const pathname = urlObj.pathname;
      
      // Format: /maps/place/Business+Name/...
      const placeMatch = pathname.match(/\/maps\/place\/([^\/]+)/);
      if (placeMatch && placeMatch[1]) {
        const query = decodeURIComponent(placeMatch[1].replace(/\+/g, ' '));
        return { query };
      }

      // Format: /maps/search/Business+Name/...
      const searchMatch = pathname.match(/\/maps\/search\/([^\/]+)/);
      if (searchMatch && searchMatch[1]) {
        const query = decodeURIComponent(searchMatch[1].replace(/\+/g, ' '));
        return { query };
      }

      // Check query parameter 'q'
      const q = urlObj.searchParams.get('q');
      if (q) {
        return { query: q };
      }

      return null;
    } catch (error) {
      console.error('Error parsing Google Maps URL:', error);
      return null;
    }
  }

  /**
   * Search for businesses using Google Maps URL
   */
  async searchFromGoogleMapsUrl(url: string, language: Locale = 'en'): Promise<ApifyBusinessInfo | null> {
    try {
      console.log('Searching from Google Maps URL:', url);
      
      // Parse URL to extract business info and location
      const urlInfo = this.parseGoogleMapsUrl(url);

      if (!urlInfo) {
        throw new Error('Invalid Google Maps URL format');
      }

      if (urlInfo.placeId) {
        return await this.fetchBusinessByPlaceId(urlInfo.placeId);
      }

      if (urlInfo.query) {
        // Try to extract location from URL or use a default
        const location = this.extractLocationFromUrl(url) || 'Netherlands'; // Default for the example URL
        console.log('🌍 Extracted location:', location, 'from URL:', url);
        console.log('🗣️ Using language:', language);
        const businesses = await this.fetchBusinessInfo(urlInfo.query, location, language);
        return businesses.length > 0 ? (businesses[0] || null) : null;
      }

      return null;
    } catch (error) {
      console.error('Error searching from Google Maps URL:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('User was not found') || error.message.includes('authentication token')) {
          throw new Error('Apify authentication failed. Please check your API token.');
        }
        throw error;
      }
      
      throw new Error(`Failed to search from Google Maps URL: ${error}`);
    }
  }

  /**
   * Validate if a business is reviewable (has reviews and is a legitimate business)
   */
  validateBusinessInfo(business: ApifyBusinessInfo): boolean {
    // Check if it has a reasonable number of reviews
    if (!business.reviewsCount || business.reviewsCount < 1) {
      return false;
    }

    // Check if it has a valid title
    if (!business.title || business.title.trim().length === 0) {
      return false;
    }

    // Check if it has valid categories (not just generic location types)
    const invalidTypes = [
      'political',
      'country',
      'administrative_area_level_1',
      'administrative_area_level_2',
      'locality',
      'route',
      'street_address',
      'postal_code',
    ];

    if (business.categories && business.categories.length > 0) {
      const hasValidCategory = business.categories.some(
        category => !invalidTypes.includes(category.toLowerCase())
      );
      if (!hasValidCategory) {
        return false;
      }
    }

    return true;
  }

  /**
   * Extract location from Google Maps URL coordinates
   */
  private extractLocationFromUrl(url: string): string | null {
    try {
      // Look for coordinates in the URL like @52.0768703,4.3054761
      const coordsMatch = url.match(/@([-\d.]+),([-\d.]+)/);
      if (coordsMatch && coordsMatch[1] && coordsMatch[2]) {
        const lat = coordsMatch[1];
        const lng = coordsMatch[2];
        
        // For the Netherlands coordinates in the example URL
        if (parseFloat(lat) > 50 && parseFloat(lat) < 54 && parseFloat(lng) > 3 && parseFloat(lng) < 8) {
          return 'Netherlands';
        }
        // Add more location mappings as needed
        return 'United States'; // Default fallback
      }
      
      return null;
    } catch (error) {
      console.error('Error extracting location from URL:', error);
      return null;
    }
  }
}
