import { ApifyClient } from 'apify-client';
import { ApifyBusinessInfo, ApifyReview } from '@/types';
import { BusinessInsert, ReviewInsert } from '@/types/supabase';
import { DatabaseService } from './database';

// Apify response types
interface ApifyReviewItem {
  reviewId?: string;
  text?: string;
  stars?: number;
  name?: string;
  publishedAtDate?: string;
  reviewImageUrls?: string[];
  authorAvatar?: string;
  authorUrl?: string;
  authorId?: string;
  updatedAtDate?: string;
  likes?: number;
  reply?: string;
  lang?: string;
}

export class ApifyScraper {
  private client: ApifyClient;
  private apiToken: string;
  private databaseService: DatabaseService;

  constructor(apiToken: string) {
    if (!apiToken || apiToken.trim() === '') {
      throw new Error('Apify API token is required');
    }
    
    console.log('Initializing Apify Scraper with token:', apiToken.substring(0, 10) + '...');
    
    this.apiToken = apiToken;
    this.client = new ApifyClient({
      token: apiToken,
    });
    this.databaseService = new DatabaseService(true); // Use service role for background operations
  }

  /**
   * Main function 1: Scrape business information and save to database
   * @param query - Search query string (e.g., "Pizza restaurant in New York")
   * @param userId - User ID to create business profile for
   * @returns Promise<string> - Business ID of the created/found business
   */
  async scrapeBusiness(query: string, userId?: string): Promise<string> {
    try {
      console.log('🚀 Starting business scraping for query:', query);

      // Step 1: Fetch business data from Apify
      const businessData = await this.fetchBusinessFromApify(query);
      
      if (!businessData) {
        throw new Error('No business found for the given query');
      }

      // Step 2: Check if business already exists in database
      let business = await this.databaseService.getBusinessByPlaceId(businessData.placeId);
      
      if (!business) {
        // Step 3: Create new business record
        const businessInsert: BusinessInsert = {
          google_place_id: businessData.placeId,
          business_name: businessData.title,
          business_address: businessData.address,
          phone_number: businessData.phone,
          website: businessData.website,
          rating: businessData.rating,
          total_reviews: businessData.reviewsCount,
          categories: businessData.categories || [],
          last_fetch_time: new Date().toISOString(),
        };

        business = await this.databaseService.createBusiness(businessInsert);
        console.log('✅ Created new business record:', business.id);
      } else {
        console.log('✅ Found existing business record:', business.id);
      }

      // Step 4: Create business profile if userId provided
      if (userId) {
        const existingProfile = await this.databaseService.getBusinessProfile(userId, business.id);
        if (!existingProfile) {
          await this.databaseService.createBusinessProfile(userId, business.id);
          console.log('✅ Created business profile for user');
        } else {
          console.log('✅ Business profile already exists for user');
        }
      }

      return business.id;
    } catch (error) {
      console.error('❌ Error in scrapeBusiness:', error);
      throw error;
    }
  }

  /**
   * Main function 2: Scrape reviews for multiple businesses
   * @param placeIds - Array of Google Place IDs
   * @param maxReviewsPerBusiness - Maximum reviews to fetch per business (default: 100)
   * @returns Promise<void>
   */
  async scrapeReviewsForBusiness(placeIds: string[], maxReviewsPerBusiness: number = 100): Promise<void> {
    try {
      console.log('🚀 Starting reviews scraping for', placeIds.length, 'businesses');

      for (const placeId of placeIds) {
        try {
          console.log(`📝 Processing reviews for place ID: ${placeId}`);

          // Step 1: Fetch reviews from Apify
          const reviewsData = await this.fetchReviewsFromApify(placeId, maxReviewsPerBusiness);
          
          if (reviewsData.length === 0) {
            console.log(`⚠️ No reviews found for place ID: ${placeId}`);
            continue;
          }

          // Step 2: Get business record
          const business = await this.databaseService.getBusinessByPlaceId(placeId);
          if (!business) {
            console.log(`⚠️ Business not found in database for place ID: ${placeId}`);
            continue;
          }

          // Step 3: Save reviews to database
          let savedCount = 0;
          for (const reviewData of reviewsData) {
            try {
              // Check if review already exists
              const existingReview = await this.databaseService.getReviewByGoogleId(reviewData.reviewId);
              
              if (!existingReview) {
                const reviewInsert: ReviewInsert = {
                  google_place_id: placeId,
                  review_text: reviewData.text,
                  rating: reviewData.rating,
                  author_name: reviewData.authorName,
                  review_date: reviewData.publishedAtDate,
                  google_review_id: reviewData.reviewId,
                  photo_urls: reviewData.photos || [],
                  language: reviewData.language,
                  author_avatar: reviewData.authorAvatar,
                  author_url: reviewData.authorUrl,
                  author_id: reviewData.authorId,
                  updated_at: reviewData.updatedAtDate,
                  likes: reviewData.likes,
                  reply: reviewData.reply,
                };

                await this.databaseService.createReview(reviewInsert);
                savedCount++;
              }
            } catch (reviewError) {
              console.error(`❌ Error saving review ${reviewData.reviewId}:`, reviewError);
            }
          }

          // Step 4: Update business last fetch time
          await this.databaseService.updateLastFetchTime(placeId);
          
          console.log(`✅ Saved ${savedCount} new reviews for place ID: ${placeId}`);
        } catch (businessError) {
          console.error(`❌ Error processing business ${placeId}:`, businessError);
        }
      }

      console.log('✅ Completed reviews scraping for all businesses');
    } catch (error) {
      console.error('❌ Error in scrapeReviewsForBusiness:', error);
      throw error;
    }
  }

  /**
   * Helper method: Fetch business data from Apify
   * @param query - Search query string
   * @returns Promise<ApifyBusinessInfo | null>
   */
  private async fetchBusinessFromApify(query: string): Promise<ApifyBusinessInfo | null> {
    try {
      const input = {
        searchStringsArray: [query],
        maxCrawledPlacesPerSearch: 1,
        language: 'en',
        searchMatching: 'all',
        placeMinimumStars: '',
        website: 'allPlaces',
        skipClosedPlaces: false,
        scrapePlaceDetailPage: false,
        scrapeTableReservationProvider: false,
        includeWebResults: false,
        scrapeDirectories: false,
        maxQuestions: 0,
        scrapeContacts: false,
        maximumLeadsEnrichmentRecords: 0,
        maxReviews: 0,
        reviewsSort: 'newest',
        reviewsFilterString: '',
        reviewsOrigin: 'all',
        scrapeReviewsPersonalData: true,
        scrapeImageAuthors: <AUTHORS>
        allPlacesNoSearchAction: ''
      };

      console.log('🔍 Fetching business from Apify with query:', query);
      
      const run = await this.client.actor('nwua9Gu5YrADL7ZDj').call(input);
      
      if (!run.defaultDatasetId) {
        throw new Error('No dataset ID returned from Apify run');
      }
      
      const { items } = await this.client.dataset(run.defaultDatasetId).listItems();

      if (!items || items.length === 0) {
        console.log('No business found for query:', query);
        return null;
      }

      const item = items[0] as Record<string, any>;
      if (!item) {
        return null;
      }

      return {
        placeId: item.placeId || '',
        title: item.title || '',
        address: item.address || '',
        phone: item.phone || '',
        website: item.website || '',
        rating: item.totalScore || 0,
        reviewsCount: item.reviewsCount || 0,
        categories: item.categories || (item.categoryName ? [item.categoryName] : []),
      };
    } catch (error) {
      console.error('❌ Error fetching business from Apify:', error);
      throw error;
    }
  }

  /**
   * Helper method: Fetch reviews data from Apify
   * @param placeId - Google Place ID
   * @param maxReviews - Maximum number of reviews to fetch
   * @returns Promise<ApifyReview[]>
   */
  private async fetchReviewsFromApify(placeId: string, maxReviews: number = 100): Promise<ApifyReview[]> {
    try {
      const input = {
        urls: [`https://maps.google.com/maps?cid=${placeId}`],
        category: 'reviews',
        keyword: '',
        lang: 'all',
        sorting: '2', // Newest first
        replies: 'all',
        source: '',
        daterange: [],
        maximum: maxReviews,
        delay: 2,
        proxy: {
          useApifyProxy: true
        }
      };

      console.log('📝 Fetching reviews from Apify for place ID:', placeId);
      
      const run = await this.client.actor('aqq1hZcxdXQPLLAi0').call(input);
      
      if (!run.defaultDatasetId) {
        throw new Error('No dataset ID returned from reviews scraper run');
      }
      
      const { items } = await this.client.dataset(run.defaultDatasetId).listItems();

      if (!items || items.length === 0) {
        console.log('No reviews found for place ID:', placeId);
        return [];
      }

      return items.map((item: ApifyReviewItem) => ({
        reviewId: item.reviewId || `${item.name}_${Date.now()}`,
        text: item.text || '',
        rating: Math.max(1, Math.min(5, item.stars || 1)),
        authorName: item.name,
        authorAvatar: item.authorAvatar,
        authorUrl: item.authorUrl,
        authorId: item.authorId,
        publishedAtDate: item.publishedAtDate || new Date().toISOString(),
        updatedAtDate: item.updatedAtDate || undefined,
        photos: item.reviewImageUrls || [],
        language: item.lang || 'en',
        likes: item.likes,
        reply: item.reply,
      }));
    } catch (error) {
      console.error('❌ Error fetching reviews from Apify:', error);
      throw error;
    }
  }
}
