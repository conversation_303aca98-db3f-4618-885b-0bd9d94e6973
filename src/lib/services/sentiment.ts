import { TextAnalyticsClient, AzureKeyCredential } from '@azure/ai-text-analytics';
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { anthropic } from '@ai-sdk/anthropic';
import { SentimentType } from '@/types';

export interface SentimentResult {
  sentiment: SentimentType;
  confidence: number;
  scores?: {
    positive: number;
    neutral: number;
    negative: number;
  };
}

export class SentimentAnalysisService {
  private azureClient?: TextAnalyticsClient;
  private aiModel: any;
  private useAzure: boolean;

  constructor(config: {
    azure?: {
      endpoint: string;
      key: string;
    };
    ai?: {
      provider: 'openai' | 'google' | 'anthropic';
      apiKey: string;
      model?: string;
    };
  }) {
    // Prefer Azure if available, fallback to AI SDK
    if (config.azure?.endpoint && config.azure?.key) {
      this.azureClient = new TextAnalyticsClient(
        config.azure.endpoint,
        new AzureKeyCredential(config.azure.key)
      );
      this.useAzure = true;
    } else if (config.ai?.provider && config.ai?.apiKey) {
      this.useAzure = false;

      // Configure AI model based on provider
      switch (config.ai.provider) {
        case 'openai':
          this.aiModel = openai(config.ai.model || 'gpt-3.5-turbo');
          break;
        case 'google':
          this.aiModel = google(config.ai.model || 'gemini-pro');
          break;
        case 'anthropic':
          this.aiModel = anthropic(config.ai.model || 'claude-3-haiku-20240307');
          break;
        default:
          throw new Error(`Unsupported AI provider: ${config.ai.provider}`);
      }
    } else {
      throw new Error('No valid AI service configuration provided');
    }
  }

  /**
   * Analyze sentiment of a single text
   */
  async analyzeSentiment(text: string): Promise<SentimentResult> {
    if (!text || text.trim().length === 0) {
      return {
        sentiment: SentimentType.NEUTRAL,
        confidence: 0,
      };
    }

    try {
      if (this.useAzure && this.azureClient) {
        return await this.analyzeWithAzure(text);
      } else if (this.aiModel) {
        return await this.analyzeWithAI(text);
      } else {
        throw new Error('No AI service available');
      }
    } catch (error) {
      console.error('Error analyzing sentiment:', error);
      // Return neutral sentiment as fallback
      return {
        sentiment: SentimentType.NEUTRAL,
        confidence: 0,
      };
    }
  }

  /**
   * Analyze sentiment using Azure Text Analytics
   */
  private async analyzeWithAzure(text: string): Promise<SentimentResult> {
    if (!this.azureClient) {
      throw new Error('Azure client not initialized');
    }

    const results = await this.azureClient.analyzeSentiment([text]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Azure sentiment analysis error: ${result.error.message}`);
    }

    const sentiment = this.mapAzureSentiment(result.sentiment);
    const confidence = result.confidenceScores[result.sentiment];

    return {
      sentiment,
      confidence,
      scores: {
        positive: result.confidenceScores.positive,
        neutral: result.confidenceScores.neutral,
        negative: result.confidenceScores.negative,
      },
    };
  }

  /**
   * Analyze sentiment using AI SDK
   */
  private async analyzeWithAI(text: string): Promise<SentimentResult> {
    if (!this.aiModel) {
      throw new Error('AI model not initialized');
    }

    const prompt = `
Analyze the sentiment of the following text and respond with ONLY a JSON object in this exact format:
{
  "sentiment": "positive" | "negative" | "neutral",
  "confidence": number between 0 and 1,
  "scores": {
    "positive": number between 0 and 1,
    "neutral": number between 0 and 1,
    "negative": number between 0 and 1
  }
}

Text to analyze: "${text}"

Respond with ONLY the JSON object, no other text.
`;

    try {
      const { text: responseText } = await generateText({
        model: this.aiModel,
        prompt,
        temperature: 0.1,
      });

      const parsed = JSON.parse(responseText.trim());

      // Validate the response structure
      if (!parsed.sentiment || !['positive', 'negative', 'neutral'].includes(parsed.sentiment)) {
        throw new Error('Invalid sentiment value');
      }

      return {
        sentiment: parsed.sentiment as SentimentType,
        confidence: parsed.confidence || 0.5,
        scores: parsed.scores || {
          positive: parsed.sentiment === 'positive' ? 0.8 : 0.2,
          neutral: parsed.sentiment === 'neutral' ? 0.8 : 0.2,
          negative: parsed.sentiment === 'negative' ? 0.8 : 0.2,
        },
      };
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);

      // Fallback: try to extract sentiment from text
      const lowerText = text.toLowerCase();
      const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'perfect', 'wonderful'];
      const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'horrible', 'worst', 'disappointing'];

      const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
      const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;

      let sentiment: SentimentType;
      let confidence: number;

      if (positiveCount > negativeCount) {
        sentiment = SentimentType.POSITIVE;
        confidence = Math.min(0.8, 0.5 + (positiveCount * 0.1));
      } else if (negativeCount > positiveCount) {
        sentiment = SentimentType.NEGATIVE;
        confidence = Math.min(0.8, 0.5 + (negativeCount * 0.1));
      } else {
        sentiment = SentimentType.NEUTRAL;
        confidence = 0.5;
      }

      return { sentiment, confidence };
    }
  }

  /**
   * Analyze sentiment for multiple texts in batch
   */
  async analyzeBatchSentiment(texts: string[]): Promise<SentimentResult[]> {
    if (this.useAzure && this.azureClient) {
      return await this.analyzeBatchWithAzure(texts);
    } else {
      // For AI SDK, process one by one (no batch API)
      const results: SentimentResult[] = [];
      for (const text of texts) {
        const result = await this.analyzeSentiment(text);
        results.push(result);
      }
      return results;
    }
  }

  /**
   * Analyze batch sentiment using Azure Text Analytics
   */
  private async analyzeBatchWithAzure(texts: string[]): Promise<SentimentResult[]> {
    if (!this.azureClient) {
      throw new Error('Azure client not initialized');
    }

    const results = await this.azureClient.analyzeSentiment(texts);
    
    return results.map(result => {
      if (result.error) {
        console.error(`Azure sentiment analysis error: ${result.error.message}`);
        return {
          sentiment: SentimentType.NEUTRAL,
          confidence: 0,
        };
      }

      const sentiment = this.mapAzureSentiment(result.sentiment);
      const confidence = result.confidenceScores[result.sentiment];

      return {
        sentiment,
        confidence,
        scores: {
          positive: result.confidenceScores.positive,
          neutral: result.confidenceScores.neutral,
          negative: result.confidenceScores.negative,
        },
      };
    });
  }

  /**
   * Map Azure sentiment to our SentimentType enum
   */
  private mapAzureSentiment(azureSentiment: string): SentimentType {
    switch (azureSentiment.toLowerCase()) {
      case 'positive':
        return SentimentType.POSITIVE;
      case 'negative':
        return SentimentType.NEGATIVE;
      case 'neutral':
      case 'mixed':
      default:
        return SentimentType.NEUTRAL;
    }
  }

  /**
   * Get sentiment distribution from an array of sentiments
   */
  getSentimentDistribution(sentiments: SentimentType[]): Record<string, number> {
    const distribution = {
      positive: 0,
      negative: 0,
      neutral: 0,
    };

    sentiments.forEach(sentiment => {
      distribution[sentiment]++;
    });

    return distribution;
  }

  /**
   * Calculate overall sentiment score from multiple results
   */
  calculateOverallSentiment(results: SentimentResult[]): {
    overallSentiment: SentimentType;
    averageConfidence: number;
    distribution: Record<string, number>;
  } {
    if (results.length === 0) {
      return {
        overallSentiment: SentimentType.NEUTRAL,
        averageConfidence: 0,
        distribution: { positive: 0, negative: 0, neutral: 0 },
      };
    }

    const sentiments = results.map(r => r.sentiment);
    const distribution = this.getSentimentDistribution(sentiments);
    const averageConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;

    // Determine overall sentiment based on majority
    let overallSentiment: SentimentType;
    if (distribution.positive > distribution.negative && distribution.positive > distribution.neutral) {
      overallSentiment = SentimentType.POSITIVE;
    } else if (distribution.negative > distribution.positive && distribution.negative > distribution.neutral) {
      overallSentiment = SentimentType.NEGATIVE;
    } else {
      overallSentiment = SentimentType.NEUTRAL;
    }

    return {
      overallSentiment,
      averageConfidence,
      distribution,
    };
  }
}
