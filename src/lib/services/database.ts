import { createClient } from '@supabase/supabase-js';
import {
  Database,
  Business,
  BusinessInsert,
  BusinessProfile,
  Review,
  ReviewInsert,
  Summary,
  Enums
} from '@/types/supabase';

type SentimentType = Enums<'sentiment_type'>;

// Create Supabase client for server-side operations
export function createServerSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}

// Create Supabase client for client-side operations
export function createClientSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

  return createClient<Database>(supabaseUrl, supabaseAnonKey);
}

export class DatabaseService {
  private supabase;

  constructor(useServiceRole = false) {
    this.supabase = useServiceRole 
      ? createServerSupabaseClient() 
      : createClientSupabaseClient();
  }

  // Business operations
  async createBusiness(business: BusinessInsert): Promise<Business> {
    const { data, error } = await this.supabase
      .from('businesses')
      .insert(business)
      .select()
      .single();

    if (error) throw new Error(`Failed to create business: ${error.message}`);
    return data;
  }

  async getBusinessByPlaceId(placeId: string): Promise<Business | null> {
    const { data, error } = await this.supabase
      .from('businesses')
      .select('*')
      .eq('google_place_id', placeId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to get business: ${error.message}`);
    }
    return data;
  }



  async getCompetitorBusinesses(params: {
    location?: string;
    category?: string;
    exclude_place_id?: string;
    limit?: number;
  }): Promise<Business[]> {
    const { location, category, exclude_place_id, limit = 15 } = params;

    let query = this.supabase
      .from('businesses')
      .select('*');

    // Filter by location (search in business_address)
    if (location) {
      query = query.ilike('business_address', `%${location}%`);
    }

    // Exclude specific business
    if (exclude_place_id) {
      query = query.neq('google_place_id', exclude_place_id);
    }

    // Limit results
    query = query.limit(limit);

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to get competitor businesses: ${error.message}`);
    }

    return data || [];
  }

  async getRecentReviews(placeId: string, limit: number = 10): Promise<Review[]> {
    const { data, error } = await this.supabase
      .from('reviews')
      .select('*')
      .eq('google_place_id', placeId)
      .order('review_date', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get recent reviews: ${error.message}`);
    }

    return data || [];
  }

  async updateBusiness(id: string, updates: Partial<Business>): Promise<Business> {
    const { data, error } = await this.supabase
      .from('businesses')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw new Error(`Failed to update business: ${error.message}`);
    return data;
  }

  async updateLastFetchTime(placeId: string): Promise<void> {
    const { error } = await this.supabase
      .from('businesses')
      .update({ last_fetch_time: new Date().toISOString() })
      .eq('google_place_id', placeId);

    if (error) throw new Error(`Failed to update last fetch time: ${error.message}`);
  }

  async getBusinessById(id: string): Promise<Business | null> {
    const { data, error } = await this.supabase
      .from('businesses')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(`Failed to get business by ID: ${error.message}`);
    }

    return data;
  }

  // Business Profile operations
  async createBusinessProfile(userId: string, businessId: string): Promise<BusinessProfile> {
    const { data, error } = await this.supabase
      .from('business_profiles')
      .insert({
        user_id: userId,
        business_id: businessId,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to create business profile: ${error.message}`);
    return data;
  }

  async getBusinessProfile(userId: string, businessId: string): Promise<BusinessProfile | null> {
    const { data, error } = await this.supabase
      .from('business_profiles')
      .select('*')
      .eq('user_id', userId)
      .eq('business_id', businessId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to get business profile: ${error.message}`);
    }
    return data;
  }

  async getUserBusinesses(userId: string): Promise<(BusinessProfile & { businesses?: Business })[]> {
    const { data, error } = await this.supabase
      .from('business_profiles')
      .select(`
        id,
        user_id,
        business_id,
        created_at,
        businesses (
          id,
          google_place_id,
          business_name,
          business_address,
          phone_number,
          website,
          last_fetch_time,
          rating,
          total_reviews,
          categories,
          created_at,
          updated_at
        )
      `)
      .eq('user_id', userId);

    if (error) {
      console.error('Database error getting user businesses:', error);
      throw new Error(`Failed to get user businesses: ${error.message}`);
    }
    
    console.log('Raw database result:', JSON.stringify(data, null, 2));
    return data;
  }

  async removeBusinessProfile(userId: string, businessId: string): Promise<void> {
    const { error } = await this.supabase
      .from('business_profiles')
      .delete()
      .eq('user_id', userId)
      .eq('business_id', businessId);

    if (error) throw new Error(`Failed to remove business profile: ${error.message}`);
  }

  // Review operations
  async createReview(review: ReviewInsert): Promise<Review> {
    const { data, error } = await this.supabase
      .from('reviews')
      .insert(review)
      .select()
      .single();

    if (error) throw new Error(`Failed to create review: ${error.message}`);
    return data;
  }

  async getReviewByGoogleId(googleReviewId: string): Promise<Review | null> {
    const { data, error } = await this.supabase
      .from('reviews')
      .select('*')
      .eq('google_review_id', googleReviewId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to get review: ${error.message}`);
    }
    return data;
  }

  async getReviewsForPlace(
    userId: string,
    placeId: string,
    options: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
      sentiment?: SentimentType;
      includeAllReviews?: boolean;
    } = {}
  ): Promise<{ reviews: Review[]; total: number }> {
    const { page = 1, limit = 10, startDate, endDate, sentiment, includeAllReviews = false } = options;
    const offset = (page - 1) * limit;

    let query = this.supabase
      .from('reviews')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .eq('google_place_id', placeId);

    if (startDate) {
      query = query.gte('review_date', startDate);
    }

    if (endDate) {
      query = query.lte('review_date', endDate);
    }

    if (sentiment) {
      query = query.eq('sentiment', sentiment);
    }

    if (!includeAllReviews) {
      // Only include reviews from last 3 months by default
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      query = query.gte('review_date', threeMonthsAgo.toISOString());
    }

    const { data, error, count } = await query
      .order('review_date', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw new Error(`Failed to get reviews: ${error.message}`);
    return { reviews: data || [], total: count || 0 };
  }

  async updateReviewSentiment(reviewId: string, sentiment: SentimentType): Promise<void> {
    const { error } = await this.supabase
      .from('reviews')
      .update({ sentiment })
      .eq('id', reviewId);

    if (error) throw new Error(`Failed to update review sentiment: ${error.message}`);
  }

  async getReviewStats(
    userId: string,
    placeId?: string,
    startDate?: string,
    endDate?: string,
    includeAllReviews = false
  ): Promise<{
    total: number;
    averageRating: number;
    sentimentDistribution: Record<string, number>;
  }> {
    let query = this.supabase
      .from('reviews')
      .select('rating, sentiment')
      .eq('user_id', userId);

    if (placeId) {
      query = query.eq('google_place_id', placeId);
    }

    if (startDate) {
      query = query.gte('review_date', startDate);
    }

    if (endDate) {
      query = query.lte('review_date', endDate);
    }

    if (!includeAllReviews) {
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      query = query.gte('review_date', threeMonthsAgo.toISOString());
    }

    const { data, error } = await query;

    if (error) throw new Error(`Failed to get review stats: ${error.message}`);

    const reviews = data || [];
    const total = reviews.length;
    const averageRating = total > 0
      ? reviews.reduce((sum, review) => sum + (review.rating || 0), 0) / total
      : 0;

    const sentimentDistribution = reviews.reduce((acc, review) => {
      const sentiment = review.sentiment || 'neutral';
      acc[sentiment] = (acc[sentiment] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return { total, averageRating, sentimentDistribution };
  }

  // Summary operations
  async createSummary(summary: Omit<Summary, 'id' | 'created_at'>): Promise<Summary> {
    const { data, error } = await this.supabase
      .from('summaries')
      .insert({
        ...summary,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to create summary: ${error.message}`);
    return data;
  }

  async getUserSummaries(userId: string): Promise<Summary[]> {
    const { data, error } = await this.supabase
      .from('summaries')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw new Error(`Failed to get summaries: ${error.message}`);
    return data || [];
  }

  // Rate limiting check
  async checkRateLimit(placeId: string, rateLimitSeconds: number): Promise<{
    canFetch: boolean;
    message: string;
    lastFetchTime?: string;
    nextAvailableTime?: string;
  }> {
    if (rateLimitSeconds === 0) {
      return { canFetch: true, message: 'Rate limiting disabled' };
    }

    const business = await this.getBusinessByPlaceId(placeId);
    if (!business || !business.last_fetch_time) {
      return { canFetch: true, message: 'No previous fetch found' };
    }

    const lastFetchTime = new Date(business.last_fetch_time);
    const timeSinceFetch = Date.now() - lastFetchTime.getTime();
    const rateLimitMs = rateLimitSeconds * 1000;

    if (timeSinceFetch < rateLimitMs) {
      const remainingMs = rateLimitMs - timeSinceFetch;
      const remainingMinutes = Math.ceil(remainingMs / (1000 * 60));
      const nextAvailableTime = new Date(lastFetchTime.getTime() + rateLimitMs).toISOString();

      return {
        canFetch: false,
        message: `Reviews updated recently. Next update available in ${remainingMinutes} minute(s).`,
        lastFetchTime: business.last_fetch_time,
        nextAvailableTime,
      };
    }

    return { canFetch: true, message: 'Rate limit check passed' };
  }
}
