import { DatabaseService } from './database';
import { Business, ApifyBusinessInfo, AuthUser } from '@/types';
import { BusinessSearchResponse } from '@/types/api';
import { type Locale } from '@/i18n/config';
import { ApifyScraper } from './apify';

export class BusinessService {
  private apifyService: ApifyScraper;
  private databaseService: DatabaseService;

  constructor(apifyApiToken: string) {
    this.apifyService = new ApifyScraper(apifyApiToken);
    this.databaseService = new DatabaseService(true); // Use service role for server operations
  }

  /**
   * Search for business from Google Maps URL
   */
  async searchFromGoogleMapsUrl(url: string, user: AuthUser, language: Locale = 'en'): Promise<BusinessSearchResponse> {
    try {
      console.log(`🚀 Business search request from user: ${user.email}`);
      console.log(`🔗 URL: ${url}`);

      // Search for business using Apify (backward compatibility)
      console.log('🔍 Step 1: Searching for business using Apify...');
      console.log(`🌐 Language: ${language}`);
      const businessInfo = await this.apifyService.searchFromGoogleMapsUrl(url, language);

      if (!businessInfo) {
        console.log('❌ Failed to find business');
        throw new Error('Could not find business information');
      }

      console.log('✅ Found business:', businessInfo.title);

      // Validate that it's a legitimate business
      console.log('🔍 Step 3: Validating business...');
      if (!this.apifyService.validateBusinessInfo(businessInfo)) {
        console.log('❌ Business validation failed - not a reviewable business');
        console.log('   Categories:', businessInfo.categories);
        console.log('   Reviews:', businessInfo.reviewsCount);
        throw new Error('The provided URL does not appear to point to a reviewable business');
      }

      console.log('✅ Business validation passed!');
      console.log(`🎉 SUCCESS: Found business '${businessInfo.title}' with ${businessInfo.reviewsCount || 0} reviews`);

      return {
        place_id: businessInfo.placeId,
        name: businessInfo.title,
        address: businessInfo.address,
        phone: businessInfo.phone,
        website: businessInfo.website,
        rating: businessInfo.rating,
        total_reviews: businessInfo.reviewsCount,
        types: businessInfo.categories,
        original_search_url: `https://maps.google.com/maps?cid=${businessInfo.placeId}`,
      };
    } catch (error) {
      console.error('❌ Error in business search:', error);
      throw error;
    }
  }

  /**
   * Save business to database after user confirmation
   */
  async saveBusiness(businessData: BusinessSearchResponse, user: AuthUser): Promise<{
    message: string;
    business_id: string;
    place_id: string;
    name: string;
  }> {
    try {
      console.log(`Saving business: ${businessData.name} for user: ${user.email}`);

      // Check if business already exists
      let business = await this.databaseService.getBusinessByPlaceId(businessData.place_id);
      let businessId: string;

      if (business) {
        // Business exists, update it
        businessId = business.id;
        console.log(`Business already exists with ID: ${businessId}`);

        business = await this.databaseService.updateBusiness(businessId, {
          business_name: businessData.name,
          business_address: businessData.address,
          phone_number: businessData.phone,
          website: businessData.website,
          rating: businessData.rating,
          total_reviews: businessData.total_reviews,
          categories: businessData.types,
          original_search_url: businessData.original_search_url,
        } as any);
      } else {
        // Create new business
        business = await this.databaseService.createBusiness({
          google_place_id: businessData.place_id,
          business_name: businessData.name,
          business_address: businessData.address,
          phone_number: businessData.phone,
          website: businessData.website,
          rating: businessData.rating,
          total_reviews: businessData.total_reviews,
          categories: businessData.types,
          original_search_url: businessData.original_search_url,
        } as any);
        businessId = business.id;
        console.log(`Created new business with ID: ${businessId}`);
      }

      // Check if user already has this business in their profile
      const existingProfile = await this.databaseService.getBusinessProfile(user.id, businessId);

      if (!existingProfile) {
        // Create business profile for user
        await this.databaseService.createBusinessProfile(user.id, businessId);
        console.log('Created business profile for user');
      } else {
        console.log('User already has this business in their profile');
      }

      return {
        message: 'Business saved successfully',
        business_id: businessId,
        place_id: businessData.place_id,
        name: businessData.name,
      };
    } catch (error) {
      console.error('Error saving business:', error);
      throw new Error(`Error saving business: ${error}`);
    }
  }

  /**
   * Get all businesses saved by the current user
   */
  async getUserBusinesses(user: AuthUser): Promise<Business[]> {
    try {
      const businessProfiles = await this.databaseService.getUserBusinesses(user.id);
      
      console.log('Business profiles received in service:', businessProfiles);
      
      // Filter out profiles where businesses relation didn't load
      const validProfiles = businessProfiles.filter(profile => profile.businesses);
      
      if (validProfiles.length === 0) {
        console.log('No valid business profiles found for user:', user.id);
        return [];
      }
      
      return validProfiles.map(profile => ({
        id: profile.businesses!.id,
        user_id: user.id,
        google_place_id: profile.businesses!.google_place_id,
        business_name: profile.businesses!.business_name,
        business_address: profile.businesses!.business_address || undefined,
        phone_number: profile.businesses!.phone_number || undefined,
        website: profile.businesses!.website || undefined,
        last_fetch_time: profile.businesses!.last_fetch_time || undefined,
        rating: profile.businesses!.rating || undefined,
        total_reviews: profile.businesses!.total_reviews || undefined,
        categories: Array.isArray(profile.businesses!.categories) ? profile.businesses!.categories as string[] : undefined,
        original_search_url: profile.businesses!.original_search_url || undefined,
        six_month_review_count: profile.businesses!.six_month_review_count || undefined,
        six_month_count_last_updated: profile.businesses!.six_month_count_last_updated || undefined,
        created_at: profile.businesses!.created_at || new Date().toISOString(),
        updated_at: profile.businesses!.updated_at || undefined,
      }));
    } catch (error) {
      console.error('Error fetching user businesses:', error);
      throw new Error(`Error fetching businesses: ${error}`);
    }
  }

  /**
   * Remove a business from user's saved businesses
   */
  async removeBusiness(businessId: string, user: AuthUser): Promise<{ message: string }> {
    try {
      await this.databaseService.removeBusinessProfile(user.id, businessId);
      return { message: 'Business removed from your saved businesses' };
    } catch (error) {
      console.error('Error removing business:', error);
      throw new Error(`Error removing business: ${error}`);
    }
  }

  /**
   * Get business details by place ID
   */
  async getBusinessByPlaceId(placeId: string): Promise<ApifyBusinessInfo | null> {
    try {
      return await this.apifyService.fetchBusinessByPlaceId(placeId);
    } catch (error) {
      console.error('Error getting business by place ID:', error);
      return null;
    }
  }
}
