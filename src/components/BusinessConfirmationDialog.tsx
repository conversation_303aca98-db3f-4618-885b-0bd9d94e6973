"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "./ui/card";
import { Alert, AlertDescription } from "./ui/alert";
import { Badge } from "./ui/badge";
import {
  Loader2,
  MapPin,
  Star,
  Phone,
  Globe,
  Users,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { apiClient } from "@/lib/api";
import type { BusinessSearchResponse } from "@/types/api";

interface BusinessConfirmationDialogProps {
  business: BusinessSearchResponse | null;
  onConfirm: (businessId: string) => void;
  onCancel: () => void;
}

export function BusinessConfirmationDialog({
  business,
  onConfirm,
  onCancel,
}: BusinessConfirmationDialogProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!business) return null;

  const handleSave = async () => {
    setIsSaving(true);
    setError(null);

    try {
      const result = await apiClient.saveBusiness(business);
      onConfirm(result.business_id);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save business");
    } finally {
      setIsSaving(false);
    }
  };

  const formatBusinessTypes = (types?: string[]) => {
    if (!types || types.length === 0) return "Business";

    const displayTypes = types
      .filter(type => !["establishment", "point_of_interest"].includes(type))
      .map(type =>
        type.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())
      );

    return displayTypes.length > 0 ? displayTypes.join(", ") : "Business";
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Confirm Business Details
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">{business.name}</h3>
            <Badge variant="secondary" className="mt-1">
              {formatBusinessTypes(business.types)}
            </Badge>
          </div>

          {business.address && (
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 mt-1 text-gray-500" />
              <span className="text-sm text-gray-700">{business.address}</span>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            {business.rating && (
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">{business.rating}/5</span>
              </div>
            )}

            {business.total_reviews && (
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-gray-500" />
                <span className="text-sm">
                  {business.total_reviews} reviews
                </span>
              </div>
            )}

            {business.phone && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <span className="text-sm">{business.phone}</span>
              </div>
            )}

            {business.website && (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-gray-500" />
                <a
                  href={business.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:underline"
                >
                  Website
                </a>
              </div>
            )}
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Is this the correct business?</strong> Once confirmed,
              you&apos;ll be able to fetch and analyze reviews for this
              location.
            </p>
          </div>
        </CardContent>

        <CardFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSaving}
            className="flex-1"
          >
            <XCircle className="mr-2 h-4 w-4" />
            Cancel
          </Button>

          <Button onClick={handleSave} disabled={isSaving} className="flex-1">
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Confirm & Save
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
