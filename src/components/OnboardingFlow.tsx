'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, Building2, MapPin, Star, Users } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile } from '@/hooks/useUserProfile';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface OnboardingData {
  firstName: string;
  googleMapsUrl: string;
  businessName: string;
  category: string;
  location: string;
}

// Step 1: User Information  
function UserInfoStep({ data, onUpdate, onNext }: {
  data: OnboardingData;
  onUpdate: (data: Partial<OnboardingData>) => void;
  onNext: () => void;
}) {
  const [isValid, setIsValid] = useState(false);

  const handleChange = (field: keyof OnboardingData, value: string) => {
    onUpdate({ [field]: value });
    setIsValid(field === 'firstName' ? value.length > 0 : data.firstName.length > 0);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <Users className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-2xl font-bold">Welcome to Review Pulse!</h2>
        <p className="text-muted-foreground">Let&apos;s get started with your information</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="firstName">First Name *</Label>
          <Input
            id="firstName"
            value={data.firstName}
            onChange={(e) => handleChange('firstName', e.target.value)}
            placeholder="Enter your first name"
          />
        </div>
      </div>

      <Button 
        onClick={onNext} 
        disabled={!isValid}
        className="w-full"
      >
        Continue
      </Button>
    </div>
  );
}

// Step 2: Business Setup
function BusinessSetupStep({ data, onUpdate, onNext, isLoading }: {
  data: OnboardingData;
  onUpdate: (data: Partial<OnboardingData>) => void;
  onNext: () => void;
  isLoading: boolean;
}) {
  const [isValid, setIsValid] = useState(false);

  const handleUrlChange = (url: string) => {
    onUpdate({ googleMapsUrl: url });
    setIsValid(url.includes('google.com/maps') || url.includes('goo.gl'));
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <Building2 className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-2xl font-bold">Add Your Business</h2>
        <p className="text-muted-foreground">We&apos;ll fetch your reviews and analyze your competition</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="googleUrl">Google Maps URL *</Label>
          <Input
            id="googleUrl"
            value={data.googleMapsUrl}
            onChange={(e) => handleUrlChange(e.target.value)}
            placeholder="https://maps.google.com/..."
          />
          <p className="text-sm text-muted-foreground mt-1">
            Copy the URL from your Google Maps business listing
          </p>
        </div>

        {data.businessName && (
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-medium mb-2">Business Preview:</h3>
            <div className="space-y-1 text-sm">
              <p><strong>Name:</strong> {data.businessName}</p>
              {data.category && <p><strong>Category:</strong> {data.category}</p>}
              {data.location && <p><strong>Location:</strong> {data.location}</p>}
            </div>
          </div>
        )}
      </div>

      <Button 
        onClick={onNext} 
        disabled={!isValid || isLoading}
        className="w-full"
      >
        {isLoading ? 'Setting up your business...' : 'Complete Setup'}
      </Button>
    </div>
  );
}

// Step 3: Completion
function CompletionStep({ onFinish }: { onFinish: () => void }) {
  return (
    <div className="space-y-6 text-center">
      <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
      <div>
        <h2 className="text-2xl font-bold">All Set!</h2>
        <p className="text-muted-foreground">
          Your dashboard is ready with your business reviews and competitor analysis
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
        <Card>
          <CardContent className="p-4">
            <Star className="h-8 w-8 text-yellow-500 mb-2" />
            <h3 className="font-medium">Review Analysis</h3>
            <p className="text-sm text-muted-foreground">
              Recent reviews analyzed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <MapPin className="h-8 w-8 text-blue-500 mb-2" />
            <h3 className="font-medium">Competitor Insights</h3>
            <p className="text-sm text-muted-foreground">
              Nearby businesses tracked
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <Building2 className="h-8 w-8 text-green-500 mb-2" />
            <h3 className="font-medium">Dashboard Ready</h3>
            <p className="text-sm text-muted-foreground">
              Analytics ready to view
            </p>
          </CardContent>
        </Card>
      </div>

      <Button onClick={onFinish} className="w-full">
        Continue to Dashboard
      </Button>
    </div>
  );
}

interface OnboardingFlowProps {
  onComplete: () => void;
}

export default function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const { user } = useAuth();
  const { updateProfile } = useUserProfile();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    firstName: '',
    googleMapsUrl: '',
    businessName: '',
    category: '',
    location: '',
  });

  const steps = [
    {
      id: 1,
      title: 'Personal Info',
      description: 'Tell us your name',
      component: UserInfoStep,
    },
    {
      id: 2,
      title: 'Business Setup',
      description: 'Add your business',
      component: BusinessSetupStep,
    },
    {
      id: 3,
      title: 'Complete',
      description: 'All set!',
      component: CompletionStep,
    },
  ];

  const handleBusinessSetup = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Step 1: Search and save the business
      const searchData = await apiClient.searchBusinessFromUrl(onboardingData.googleMapsUrl);

      // Update business info with fetched data
      setOnboardingData(prev => ({
        ...prev,
        businessName: searchData.name,
        category: searchData.types?.[0] || '',
        location: searchData.address || '',
      }));

      // Step 2: Save business to user profile
      await apiClient.saveBusiness({
        place_id: searchData.place_id,
        name: searchData.name,
        address: searchData.address,
        phone: searchData.phone,
        website: searchData.website,
        rating: searchData.rating,
        total_reviews: searchData.total_reviews,
        types: searchData.types,
      });

      // Step 3: Fetch reviews for the business
      await apiClient.fetchReviews(searchData.place_id, searchData.name);

      // Step 4: Setup competitor analysis
      await apiClient.post('/api/competitors/setup', {
        business_location: searchData.address || '',
        business_category: searchData.types?.[0] || 'business',
        exclude_place_id: searchData.place_id,
      });

      toast.success('Business setup completed successfully!');
      setCurrentStep(3);
    } catch (error) {
      console.error('Onboarding error:', error);
      toast.error(error instanceof Error ? error.message : 'Setup failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStepNext = async () => {
    if (currentStep === 1) {
      // Save user profile information
      try {
        await updateProfile({
          full_name: onboardingData.firstName,
          business_role: 'Owner', // Default role since we're not asking
          business_description: '', // Not collecting this anymore
        });
        setCurrentStep(prev => prev + 1);
      } catch (error) {
        console.error('Error saving user profile:', error);
        toast.error('Failed to save user information');
      }
    } else if (currentStep === 2) {
      handleBusinessSetup();
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleFinish = async () => {
    if (!user) return;

    try {
      // Mark onboarding as completed
      await updateProfile({
        onboarding_completed: true,
      });
      toast.success('Onboarding completed!');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      // Don't block completion on profile save failure
    }
    
    // Call the completion callback
    onComplete();
  };

  const CurrentStepComponent = steps[currentStep - 1]?.component;
  const progress = (currentStep / steps.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <div className="flex items-center justify-between mb-4">
            <div>
              <CardTitle>Welcome to Review Pulse</CardTitle>
              <CardDescription>
                Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.description}
              </CardDescription>
            </div>
            <div className="text-sm text-muted-foreground">
              {Math.round(progress)}% Complete
            </div>
          </div>
          <Progress value={progress} className="w-full" />
        </CardHeader>

        <CardContent>
          {CurrentStepComponent && (
            <CurrentStepComponent
              data={onboardingData}
              onUpdate={setOnboardingData}
              onNext={handleStepNext}
              onFinish={handleFinish}
              isLoading={isLoading}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}