"use client";

import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/providers/LanguageProvider";
import { LanguageToggle } from "@/providers/LanguageProvider";
import { ThemeToggle } from "@/providers/ThemeProvider";

interface GlobalHeaderProps {
  showUserInfo?: boolean;
  className?: string;
}

export function GlobalHeader({ showUserInfo = false, className = "" }: GlobalHeaderProps) {
  const router = useRouter();
  const { user, signOut } = useAuth();
  const { t } = useLanguage();

  const handleLogoClick = () => {
    if (user) {
      // If authenticated, go to dashboard overview
      router.push("/dashboard/overview");
    } else {
      // If not authenticated, go to home/login
      router.push("/");
    }
  };

  return (
    <header className={`bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo - Clickable */}
          <div className="flex items-center">
            <button
              onClick={handleLogoClick}
              className="text-xl font-semibold text-gray-900 dark:text-white hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md px-2 py-1"
              title={user ? "Go to Dashboard" : "Go to Home"}
            >
              {t("pages.dashboard.appName")}
            </button>
          </div>

          {/* Right side - Controls and User Info */}
          <div className="flex items-center space-x-3">
            {/* Language Toggle */}
            <LanguageToggle />
            
            {/* Theme Toggle */}
            <ThemeToggle />

            {/* User Info and Sign Out (only show if requested and user is authenticated) */}
            {showUserInfo && user && (
              <>
                <div className="hidden sm:block">
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {user.email}
                  </span>
                </div>
                <button
                  onClick={signOut}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                  {t("pages.dashboard.signOut")}
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
