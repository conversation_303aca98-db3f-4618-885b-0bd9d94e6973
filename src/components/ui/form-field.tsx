"use client";

import * as React from "react";
import {
  useF<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Controller,
  FieldPath,
  FieldValues,
} from "react-hook-form";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";

interface BaseFieldProps {
  label?: string;
  description?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
}

interface FormFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> extends BaseFieldProps {
  name: TName;
  children?: React.ReactNode;
}

/**
 * Base form field wrapper that provides consistent styling and error handling
 */
export function FormField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  name,
  label,
  description,
  required,
  className,
  children,
}: FormFieldProps<TFieldValues, TName>) {
  const {
    formState: { errors },
  } = useFormContext<TFieldValues>();
  const error = errors[name];

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label
          htmlFor={name}
          className={cn(
            required &&
              "after:content-['*'] after:ml-0.5 after:text-destructive"
          )}
        >
          {label}
        </Label>
      )}
      {children}
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      {error && (
        <p className="text-sm text-destructive" role="alert">
          {error.message as string}
        </p>
      )}
    </div>
  );
}

/**
 * Input field with form integration
 */
interface InputFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> extends BaseFieldProps {
  name: TName;
  type?: React.HTMLInputTypeAttribute;
  placeholder?: string;
  autoComplete?: string;
}

export function InputField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  name,
  type = "text",
  placeholder,
  autoComplete,
  ...fieldProps
}: InputFieldProps<TFieldValues, TName>) {
  const {
    control,
    formState: { errors },
  } = useFormContext<TFieldValues>();
  const error = errors[name];

  return (
    <FormField name={name} {...fieldProps}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Input
            {...field}
            id={name}
            type={type}
            placeholder={placeholder}
            autoComplete={autoComplete}
            disabled={fieldProps.disabled}
            className={cn(
              error && "border-destructive focus-visible:ring-destructive"
            )}
            aria-invalid={!!error}
            aria-describedby={error ? `${name}-error` : undefined}
          />
        )}
      />
    </FormField>
  );
}

/**
 * Textarea field with form integration
 */
interface TextareaFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> extends BaseFieldProps {
  name: TName;
  placeholder?: string;
  rows?: number;
}

export function TextareaField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  name,
  placeholder,
  rows = 3,
  ...fieldProps
}: TextareaFieldProps<TFieldValues, TName>) {
  const {
    control,
    formState: { errors },
  } = useFormContext<TFieldValues>();
  const error = errors[name];

  return (
    <FormField name={name} {...fieldProps}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Textarea
            {...field}
            id={name}
            placeholder={placeholder}
            rows={rows}
            disabled={fieldProps.disabled}
            className={cn(
              error && "border-destructive focus-visible:ring-destructive"
            )}
            aria-invalid={!!error}
            aria-describedby={error ? `${name}-error` : undefined}
          />
        )}
      />
    </FormField>
  );
}

/**
 * Select field with form integration
 */
interface SelectFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> extends BaseFieldProps {
  name: TName;
  placeholder?: string;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
}

export function SelectField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  name,
  placeholder = "Select an option",
  options,
  ...fieldProps
}: SelectFieldProps<TFieldValues, TName>) {
  const {
    control,
    formState: { errors },
  } = useFormContext<TFieldValues>();
  const error = errors[name];

  return (
    <FormField name={name} {...fieldProps}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Select
            onValueChange={field.onChange}
            value={field.value}
            disabled={fieldProps.disabled}
          >
            <SelectTrigger
              id={name}
              className={cn(
                error && "border-destructive focus:ring-destructive"
              )}
              aria-invalid={!!error}
              aria-describedby={error ? `${name}-error` : undefined}
            >
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map(option => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      />
    </FormField>
  );
}

/**
 * Checkbox field with form integration
 */
interface CheckboxFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> extends BaseFieldProps {
  name: TName;
}

export function CheckboxField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  name,
  label,
  description,
  ...fieldProps
}: CheckboxFieldProps<TFieldValues, TName>) {
  const {
    control,
    formState: { errors },
  } = useFormContext<TFieldValues>();
  const error = errors[name];

  return (
    <div className={cn("space-y-2", fieldProps.className)}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={name}
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={fieldProps.disabled}
              aria-invalid={!!error}
              aria-describedby={error ? `${name}-error` : undefined}
            />
            {label && (
              <Label
                htmlFor={name}
                className={cn(
                  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                  fieldProps.required &&
                    "after:content-['*'] after:ml-0.5 after:text-destructive"
                )}
              >
                {label}
              </Label>
            )}
          </div>
        )}
      />
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      {error && (
        <p
          className="text-sm text-destructive"
          role="alert"
          id={`${name}-error`}
        >
          {error.message as string}
        </p>
      )}
    </div>
  );
}

/**
 * Switch field with form integration
 */
interface SwitchFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> extends BaseFieldProps {
  name: TName;
}

export function SwitchField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  name,
  label,
  description,
  ...fieldProps
}: SwitchFieldProps<TFieldValues, TName>) {
  const {
    control,
    formState: { errors },
  } = useFormContext<TFieldValues>();
  const error = errors[name];

  return (
    <div className={cn("space-y-2", fieldProps.className)}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div className="flex items-center space-x-2">
            <Switch
              id={name}
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={fieldProps.disabled}
              aria-invalid={!!error}
              aria-describedby={error ? `${name}-error` : undefined}
            />
            {label && (
              <Label
                htmlFor={name}
                className={cn(
                  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                  fieldProps.required &&
                    "after:content-['*'] after:ml-0.5 after:text-destructive"
                )}
              >
                {label}
              </Label>
            )}
          </div>
        )}
      />
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      {error && (
        <p
          className="text-sm text-destructive"
          role="alert"
          id={`${name}-error`}
        >
          {error.message as string}
        </p>
      )}
    </div>
  );
}
