"use client";

import * as React from "react";
import {
  use<PERSON><PERSON>,
  Form<PERSON><PERSON>ider,
  SubmitHandler,
  UseFormProps,
  FieldValues,
  useFormContext as useRHFContext,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type { ZodSchema } from "zod";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";

interface FormWrapperProps<TFormValues extends FieldValues> {
  schema: ZodSchema<TFormValues>;
  onSubmit: SubmitHandler<TFormValues>;
  defaultValues?: UseFormProps<TFormValues>["defaultValues"];
  children: React.ReactNode;

  // Form styling and behavior
  className?: string;
  disabled?: boolean;

  // Submit button configuration
  submitText?: string;
  submitVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  showSubmitButton?: boolean;

  // Card wrapper configuration
  title?: string;
  description?: string;
  showCard?: boolean;

  // Error handling
  error?: string | null;
  onError?: (error: unknown) => void;

  // Loading state
  isLoading?: boolean;

  // Additional form options
  mode?: "onChange" | "onBlur" | "onSubmit" | "onTouched" | "all";
  reValidateMode?: "onChange" | "onBlur" | "onSubmit";
}

/**
 * Comprehensive form wrapper that integrates React Hook Form with Zod validation
 * and provides consistent styling and error handling
 */
export function FormWrapper<TFormValues extends FieldValues>({
  schema,
  onSubmit,
  defaultValues,
  children,
  className,
  disabled = false,
  submitText = "Submit",
  submitVariant = "default",
  showSubmitButton = true,
  title,
  description,
  showCard = false,
  error,
  onError,
  isLoading = false,
  mode = "onSubmit",
  reValidateMode = "onChange",
}: FormWrapperProps<TFormValues>) {
  const methods = useForm<TFormValues>({
    // @ts-expect-error - zodResolver type compatibility issue with strict types
    resolver: zodResolver(schema),
    defaultValues,
    mode,
    reValidateMode,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;
  const isFormDisabled = disabled || isLoading || isSubmitting;

  const onSubmitHandler: SubmitHandler<TFormValues> = async data => {
    try {
      await onSubmit(data);
    } catch (err) {
      if (onError) {
        onError(err);
      }
    }
  };

  const formContent = (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit(onSubmitHandler as SubmitHandler<FieldValues>)}
        className={cn("space-y-6", className)}
        noValidate
      >
        {/* Global error message */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Form fields */}
        <div className="space-y-4">{children}</div>

        {/* Submit button */}
        {showSubmitButton && (
          <Button
            type="submit"
            variant={submitVariant}
            disabled={isFormDisabled}
            className="w-full"
          >
            {(isLoading || isSubmitting) && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {submitText}
          </Button>
        )}
      </form>
    </FormProvider>
  );

  if (showCard) {
    return (
      <Card className={className}>
        {(title || description) && (
          <CardHeader>
            {title && <CardTitle>{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
        )}
        <CardContent>{formContent}</CardContent>
      </Card>
    );
  }

  return formContent;
}

/**
 * Hook to access form methods from within form components
 */
export function useFormContext<
  TFormValues extends FieldValues = FieldValues,
>() {
  return useRHFContext<TFormValues>();
}

/**
 * Form section component for organizing form fields
 */
interface FormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function FormSection({
  title,
  description,
  children,
  className,
}: FormSectionProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium leading-none tracking-tight">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">{children}</div>
    </div>
  );
}

/**
 * Form actions component for custom button layouts
 */
interface FormActionsProps {
  children: React.ReactNode;
  className?: string;
  align?: "left" | "center" | "right" | "between";
}

export function FormActions({
  children,
  className,
  align = "right",
}: FormActionsProps) {
  const alignClasses = {
    left: "justify-start",
    center: "justify-center",
    right: "justify-end",
    between: "justify-between",
  };

  return (
    <div className={cn("flex gap-2", alignClasses[align], className)}>
      {children}
    </div>
  );
}

/**
 * Form field group for related fields
 */
interface FormFieldGroupProps {
  children: React.ReactNode;
  className?: string;
  columns?: 1 | 2 | 3 | 4;
}

export function FormFieldGroup({
  children,
  className,
  columns = 1,
}: FormFieldGroupProps) {
  const columnClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  };

  return (
    <div className={cn("grid gap-4", columnClasses[columns], className)}>
      {children}
    </div>
  );
}
