import * as React from "react";
import { cn } from "@/lib/utils";
import { themeClasses } from "@/lib/theme";

interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  variant?:
    | "h1"
    | "h2"
    | "h3"
    | "h4"
    | "h5"
    | "h6"
    | "body"
    | "muted"
    | "small"
    | "lead";
  as?: React.ElementType;
}

const Typography = React.forwardRef<HTMLElement, TypographyProps>(
  ({ className, variant = "body", as, children, ...props }, ref) => {
    const Component = as || getDefaultElement(variant);

    const variantClasses = {
      h1: themeClasses.heading.h1,
      h2: themeClasses.heading.h2,
      h3: themeClasses.heading.h3,
      h4: themeClasses.heading.h4,
      h5: themeClasses.heading.h5,
      h6: themeClasses.heading.h6,
      body: themeClasses.text.body,
      muted: themeClasses.text.muted,
      small: themeClasses.text.small,
      lead: themeClasses.text.lead,
    };

    return (
      <Component
        ref={ref}
        className={cn(variantClasses[variant], className)}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Typography.displayName = "Typography";

function getDefaultElement(
  variant: TypographyProps["variant"]
): React.ElementType {
  switch (variant) {
    case "h1":
      return "h1";
    case "h2":
      return "h2";
    case "h3":
      return "h3";
    case "h4":
      return "h4";
    case "h5":
      return "h5";
    case "h6":
      return "h6";
    case "lead":
      return "p";
    case "small":
      return "small";
    case "muted":
      return "p";
    default:
      return "p";
  }
}

export { Typography };

/**
 * Pre-configured typography components for common use cases
 */
export const H1 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h1 ref={ref} className={cn(themeClasses.heading.h1, className)} {...props} />
));
H1.displayName = "H1";

export const H2 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h2 ref={ref} className={cn(themeClasses.heading.h2, className)} {...props} />
));
H2.displayName = "H2";

export const H3 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3 ref={ref} className={cn(themeClasses.heading.h3, className)} {...props} />
));
H3.displayName = "H3";

export const H4 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h4 ref={ref} className={cn(themeClasses.heading.h4, className)} {...props} />
));
H4.displayName = "H4";

export const Text = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn(themeClasses.text.body, className)} {...props} />
));
Text.displayName = "Text";

export const Muted = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn(themeClasses.text.muted, className)} {...props} />
));
Muted.displayName = "Muted";

export const Small = React.forwardRef<
  HTMLElement,
  React.HTMLAttributes<HTMLElement>
>(({ className, ...props }, ref) => (
  <small
    ref={ref}
    className={cn(themeClasses.text.small, className)}
    {...props}
  />
));
Small.displayName = "Small";

export const Lead = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn(themeClasses.text.lead, className)} {...props} />
));
Lead.displayName = "Lead";
