"use client";

import { useState } from "react";
import { z } from "zod";
import {
  FormWrapper,
  FormSection,
  FormFieldGroup,
  FormActions,
} from "@/components/ui/form-wrapper";
import {
  InputField,
  TextareaField,
  SelectField,
  CheckboxField,
  SwitchField,
} from "@/components/ui/form-field";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { H3, Text } from "@/components/ui/typography";
import { ThemeToggle } from "@/providers/ThemeProvider";

// Example schema demonstrating various field types and validations
const exampleSchema = z.object({
  // Personal Information
  firstName: z
    .string()
    .min(1, "First name is required")
    .max(50, "First name is too long"),
  lastName: z
    .string()
    .min(1, "Last name is required")
    .max(50, "Last name is too long"),
  email: z.string().email("Please enter a valid email address"),
  phone: z
    .string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number")
    .optional(),

  // Business Information
  company: z.string().min(1, "Company name is required"),
  jobTitle: z.string().min(1, "Job title is required"),
  industry: z.string().min(1, "Please select an industry"),

  // Preferences
  bio: z.string().max(500, "Bio is too long").optional(),
  newsletter: z.boolean().default(false),
  notifications: z.boolean().default(true),

  // Terms
  terms: z
    .boolean()
    .refine(val => val === true, "You must accept the terms and conditions"),
});

type ExampleFormData = z.infer<typeof exampleSchema>;

const industryOptions = [
  { value: "technology", label: "Technology" },
  { value: "healthcare", label: "Healthcare" },
  { value: "finance", label: "Finance" },
  { value: "education", label: "Education" },
  { value: "retail", label: "Retail" },
  { value: "manufacturing", label: "Manufacturing" },
  { value: "other", label: "Other" },
];

export function ExampleForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitResult, setSubmitResult] = useState<string | null>(null);

  const handleSubmit = async (data: ExampleFormData) => {
    setIsSubmitting(true);
    setSubmitResult(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log("Form submitted:", data);
      setSubmitResult("Form submitted successfully!");
    } catch {
      setSubmitResult("Failed to submit form. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <H3>Example Form Components</H3>
          <Text className="text-muted-foreground">
            Demonstrating shadcn/ui + React Hook Form + Zod integration
          </Text>
        </div>
        <ThemeToggle />
      </div>

      {/* Main Form */}
      <Card>
        <CardHeader>
          <CardTitle>User Registration Form</CardTitle>
        </CardHeader>
        <CardContent>
          <FormWrapper
            schema={exampleSchema}
            onSubmit={handleSubmit}
            isLoading={isSubmitting}
            submitText={isSubmitting ? "Submitting..." : "Submit Form"}
            showSubmitButton={false}
            defaultValues={{
              firstName: "",
              lastName: "",
              email: "",
              phone: "",
              company: "",
              jobTitle: "",
              industry: "",
              bio: "",
              newsletter: false,
              notifications: true,
              terms: false,
            }}
          >
            <FormSection
              title="Personal Information"
              description="Please provide your basic information"
            >
              <FormFieldGroup columns={2}>
                <InputField
                  name="firstName"
                  label="First Name"
                  placeholder="Enter your first name"
                  required
                />
                <InputField
                  name="lastName"
                  label="Last Name"
                  placeholder="Enter your last name"
                  required
                />
              </FormFieldGroup>

              <FormFieldGroup columns={2}>
                <InputField
                  name="email"
                  type="email"
                  label="Email Address"
                  placeholder="Enter your email"
                  autoComplete="email"
                  required
                />
                <InputField
                  name="phone"
                  type="tel"
                  label="Phone Number"
                  placeholder="+****************"
                  autoComplete="tel"
                />
              </FormFieldGroup>
            </FormSection>

            <FormSection
              title="Business Information"
              description="Tell us about your work"
            >
              <FormFieldGroup columns={2}>
                <InputField
                  name="company"
                  label="Company"
                  placeholder="Enter your company name"
                  required
                />
                <InputField
                  name="jobTitle"
                  label="Job Title"
                  placeholder="Enter your job title"
                  required
                />
              </FormFieldGroup>

              <SelectField
                name="industry"
                label="Industry"
                placeholder="Select your industry"
                options={industryOptions}
                required
              />

              <TextareaField
                name="bio"
                label="Bio"
                placeholder="Tell us a bit about yourself..."
                description="Optional: Share a brief description about yourself (max 500 characters)"
                rows={4}
              />
            </FormSection>

            <FormSection
              title="Preferences"
              description="Customize your experience"
            >
              <div className="space-y-4">
                <CheckboxField
                  name="newsletter"
                  label="Subscribe to newsletter"
                  description="Receive updates about new features and company news"
                />

                <SwitchField
                  name="notifications"
                  label="Enable notifications"
                  description="Get notified about important updates and activities"
                />

                <CheckboxField
                  name="terms"
                  label="I accept the terms and conditions"
                  description="You must accept our terms and conditions to continue"
                  required
                />
              </div>
            </FormSection>

            <FormActions align="between">
              <Button type="button" variant="outline">
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-[120px]"
              >
                {isSubmitting ? "Submitting..." : "Submit Form"}
              </Button>
            </FormActions>
          </FormWrapper>
        </CardContent>
      </Card>

      {/* Result Display */}
      {submitResult && (
        <Card>
          <CardContent className="pt-6">
            <Text
              className={
                submitResult.includes("success")
                  ? "text-green-600"
                  : "text-red-600"
              }
            >
              {submitResult}
            </Text>
          </CardContent>
        </Card>
      )}

      {/* Component Showcase */}
      <Card>
        <CardHeader>
          <CardTitle>Component Showcase</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <H3 className="mb-4">Available Form Components</H3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Input Components:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                  <li>InputField - Text, email, password, tel, url, etc.</li>
                  <li>TextareaField - Multi-line text input</li>
                  <li>SelectField - Dropdown selection</li>
                </ul>
              </div>
              <div>
                <strong>Choice Components:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                  <li>CheckboxField - Boolean selection</li>
                  <li>SwitchField - Toggle switch</li>
                  <li>RadioGroupField - Single choice from options</li>
                </ul>
              </div>
            </div>
          </div>

          <div>
            <H3 className="mb-4">Layout Components</H3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Organization:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                  <li>FormSection - Group related fields</li>
                  <li>FormFieldGroup - Responsive field layouts</li>
                  <li>FormActions - Button arrangements</li>
                </ul>
              </div>
              <div>
                <strong>Wrapper:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                  <li>FormWrapper - Complete form with validation</li>
                  <li>Automatic error handling</li>
                  <li>Loading states and submission</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
