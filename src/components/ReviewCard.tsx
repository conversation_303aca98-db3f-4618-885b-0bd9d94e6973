"use client";

import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { Star, User, Calendar, Image as ImageIcon } from "lucide-react";
import { useState } from "react";
import Image from "next/image";
import { Review } from "@/hooks/useReviews";

interface ReviewCardProps {
  review: Review & { photo_urls?: string[] };
}

interface PhotoGalleryProps {
  photos: string[];
}

const PhotoGallery = ({ photos }: PhotoGalleryProps) => {
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);

  if (!photos || photos.length === 0) return null;

  return (
    <div className="mt-3">
      <div className="flex items-center gap-2 mb-2">
        <ImageIcon className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">
          {photos.length} photo{photos.length !== 1 ? "s" : ""}
        </span>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
        {photos.slice(0, 6).map((photo, index) => (
          <div
            key={index}
            className="relative aspect-square rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
            onClick={() => setSelectedPhoto(photo)}
          >
            <Image
              src={photo}
              alt={`Review photo ${index + 1}`}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100px, 120px"
              onError={e => {
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
              }}
            />
            {index === 5 && photos.length > 6 && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  +{photos.length - 6} more
                </span>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Photo Modal */}
      {selectedPhoto && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedPhoto(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <Image
              src={selectedPhoto}
              alt="Review photo"
              width={800}
              height={600}
              className="max-w-full max-h-full object-contain rounded-lg"
              sizes="(max-width: 768px) 100vw, 800px"
            />
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75"
              onClick={() => setSelectedPhoto(null)}
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export function ReviewCard({ review }: ReviewCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getSentimentColor = (sentiment?: string) => {
    switch (sentiment?.toLowerCase()) {
      case "positive":
        return "bg-green-100 text-green-800 border-green-200";
      case "negative":
        return "bg-red-100 text-red-800 border-red-200";
      case "neutral":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header with author and rating */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                <User className="h-4 w-4 text-gray-600" />
              </div>
              <div>
                <div className="font-medium text-sm">{review.author_name}</div>
                <div className="flex items-center gap-1">
                  {renderStars(review.rating)}
                  <span className="text-sm text-gray-600 ml-1">
                    {review.rating}/5
                  </span>
                </div>
              </div>
            </div>

            {/* Sentiment Badge */}
            {review.sentiment && (
              <Badge
                variant="outline"
                className={getSentimentColor(review.sentiment)}
              >
                {review.sentiment}
                {review.sentiment_score && (
                  <span className="ml-1">
                    ({(review.sentiment_score * 100).toFixed(0)}%)
                  </span>
                )}
              </Badge>
            )}
          </div>

          {/* Review Text */}
          <div className="text-sm text-gray-700 leading-relaxed">
            {review.text}
          </div>

          {/* Photo Gallery */}
          <PhotoGallery photos={review.photo_urls || []} />

          {/* Footer with date */}
          <div className="flex items-center gap-1 text-xs text-gray-500 pt-2 border-t border-gray-100">
            <Calendar className="h-3 w-3" />
            <span>{formatDate(review.time)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
