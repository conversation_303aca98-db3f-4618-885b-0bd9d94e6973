"use client";

import { Review } from "@/hooks/useReviews";

interface SentimentChartProps {
  reviews: Review[];
}

export function SentimentChart({ reviews }: SentimentChartProps) {
  const getSentimentStats = () => {
    const stats = { positive: 0, negative: 0, neutral: 0 };

    reviews.forEach(review => {
      const sentiment = review.sentiment?.toLowerCase();
      if (sentiment === "positive") stats.positive++;
      else if (sentiment === "negative") stats.negative++;
      else stats.neutral++;
    });

    return stats;
  };

  const stats = getSentimentStats();
  const total = reviews.length;

  if (total === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No sentiment data available
      </div>
    );
  }

  const positivePercentage = (stats.positive / total) * 100;
  const negativePercentage = (stats.negative / total) * 100;
  const neutralPercentage = (stats.neutral / total) * 100;

  return (
    <div className="space-y-6">
      {/* Progress Bars */}
      <div className="space-y-4">
        {/* Positive */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-green-700">Positive</span>
            <span className="text-sm text-gray-600">
              {stats.positive} ({positivePercentage.toFixed(1)}%)
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${positivePercentage}%` }}
            />
          </div>
        </div>

        {/* Neutral */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Neutral</span>
            <span className="text-sm text-gray-600">
              {stats.neutral} ({neutralPercentage.toFixed(1)}%)
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gray-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${neutralPercentage}%` }}
            />
          </div>
        </div>

        {/* Negative */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-red-700">Negative</span>
            <span className="text-sm text-gray-600">
              {stats.negative} ({negativePercentage.toFixed(1)}%)
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-red-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${negativePercentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {stats.positive}
          </div>
          <div className="text-xs text-gray-600">Positive</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-600">
            {stats.neutral}
          </div>
          <div className="text-xs text-gray-600">Neutral</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">
            {stats.negative}
          </div>
          <div className="text-xs text-gray-600">Negative</div>
        </div>
      </div>

      {/* Overall Sentiment Score */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="text-center">
          <div className="text-sm text-gray-600 mb-1">Overall Sentiment</div>
          <div className="text-lg font-semibold">
            {positivePercentage > negativePercentage ? (
              <span className="text-green-600">
                {positivePercentage > 60 ? "Very Positive" : "Positive"}
              </span>
            ) : negativePercentage > positivePercentage ? (
              <span className="text-red-600">
                {negativePercentage > 60 ? "Very Negative" : "Negative"}
              </span>
            ) : (
              <span className="text-gray-600">Neutral</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
