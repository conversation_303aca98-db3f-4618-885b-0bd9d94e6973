"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "./ui/card";
import { MapPin } from "lucide-react";
import { apiClient } from "@/lib/api";
import type { BusinessSearchResponse } from "@/types/api";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { InputField } from "@/components/ui/form-field";
import {
  googleMapsUrlSchema,
  type GoogleMapsUrlFormData,
} from "@/lib/validations";
import { useBusinessTranslations, useFormTranslations } from "@/lib/i18n";

interface GoogleMapsUrlInputProps {
  onBusinessFound: (business: BusinessSearchResponse) => void;
}

export function GoogleMapsUrlInput({
  onBusinessFound,
}: GoogleMapsUrlInputProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const t = useBusinessTranslations();
  const tf = useFormTranslations();

  const handleSubmit = async (data: GoogleMapsUrlFormData) => {
    setError(null);
    setIsLoading(true);

    try {
      const business = await apiClient.searchBusinessFromUrl(data.url.trim());
      onBusinessFound(business);
    } catch (err) {
      setError(err instanceof Error ? err.message : t("failedToExtract"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          {t("addFromGoogleMaps")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <FormWrapper
          schema={googleMapsUrlSchema}
          onSubmit={handleSubmit}
          error={error}
          isLoading={isLoading}
          submitText={
            isLoading
              ? tf("buttons.extractingBusinessInfo")
              : tf("buttons.extractBusinessInfo")
          }
          showSubmitButton={true}
        >
          <InputField
            name="url"
            type="url"
            label={tf("labels.googleMapsUrl")}
            placeholder={tf("placeholders.googleMapsUrl")}
            description={tf("descriptions.googleMapsUrl")}
            required
          />
        </FormWrapper>

        <div className="mt-4 space-y-3">
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm font-medium mb-2">{t("howToGetLink")}</p>
            <ol className="text-xs text-muted-foreground space-y-1">
              <li>{t("step1")}</li>
              <li>{t("step2")}</li>
              <li>{t("step3")}</li>
              <li>{t("step4")}</li>
            </ol>
          </div>

          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <span className="font-medium">📅 Recent Reviews:</span> We fetch
              reviews from the latest 3 months to ensure you get the most
              current customer feedback and insights.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
