"use client";

import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { Star, TrendingUp, MessageSquare, Calendar } from "lucide-react";
import type { BusinessInfo } from "@/types/api";
import { ReviewsResponse } from "@/hooks/useReviews";

interface BusinessHeaderProps {
  business: BusinessInfo;
  reviewsData?: ReviewsResponse;
}

export function BusinessHeader({ business, reviewsData }: BusinessHeaderProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getSentimentStats = () => {
    if (!reviewsData?.reviews) return { positive: 0, negative: 0, neutral: 0 };

    const stats = { positive: 0, negative: 0, neutral: 0 };
    reviewsData.reviews.forEach(review => {
      const sentiment = review.sentiment?.toLowerCase();
      if (sentiment === "positive") stats.positive++;
      else if (sentiment === "negative") stats.negative++;
      else stats.neutral++;
    });

    return stats;
  };

  const sentimentStats = getSentimentStats();
  const totalReviews = reviewsData?.total || 0;
  const averageRating = reviewsData?.average_rating || 0;

  return (
    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          {/* Business Info */}
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {business.business_name}
            </h1>

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Added {formatDate(business.created_at)}</span>
              </div>

              {business.updated_at && (
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-4 w-4" />
                  <span>Updated {formatDate(business.updated_at)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
            {/* Total Reviews */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <MessageSquare className="h-5 w-5 text-blue-600 mr-1" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {totalReviews}
              </div>
              <div className="text-xs text-gray-600">Total Reviews</div>
            </div>

            {/* Average Rating */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <Star className="h-5 w-5 text-yellow-500 mr-1" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {averageRating > 0 ? averageRating.toFixed(1) : "—"}
              </div>
              <div className="text-xs text-gray-600">Avg Rating</div>
            </div>

            {/* Positive Sentiment */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {sentimentStats.positive}
              </div>
              <div className="text-xs text-gray-600">Positive</div>
            </div>

            {/* Negative Sentiment */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
              </div>
              <div className="text-2xl font-bold text-red-600">
                {sentimentStats.negative}
              </div>
              <div className="text-xs text-gray-600">Negative</div>
            </div>
          </div>
        </div>

        {/* Sentiment Badges */}
        {totalReviews > 0 && (
          <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-blue-200">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              {((sentimentStats.positive / totalReviews) * 100).toFixed(1)}%
              Positive
            </Badge>
            <Badge variant="secondary" className="bg-gray-100 text-gray-800">
              {((sentimentStats.neutral / totalReviews) * 100).toFixed(1)}%
              Neutral
            </Badge>
            <Badge variant="secondary" className="bg-red-100 text-red-800">
              {((sentimentStats.negative / totalReviews) * 100).toFixed(1)}%
              Negative
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
