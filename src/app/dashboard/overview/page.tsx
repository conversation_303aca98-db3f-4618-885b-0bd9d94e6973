'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Building2, 
  Star, 
  TrendingUp, 
  Users, 
  MapPin, 
  Calendar,
  BarChart3,
  Eye
} from 'lucide-react';
import { useBusinesses } from '@/hooks/useBusinesses';
import { useAuth } from '@/hooks/useAuth';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface CompetitorBusiness {
  id: string;
  place_id: string;
  name: string;
  address: string;
  rating: number;
  reviews_count: number;
  category: string;
  recent_reviews: Array<{
    id: string;
    review_text: string;
    rating: number;
    author_name: string;
    review_date: string;
    sentiment: 'positive' | 'negative' | 'neutral';
  }>;
}

interface BusinessStats {
  total_reviews: number;
  average_rating: number;
  sentiment_distribution: {
    positive: number;
    negative: number;
    neutral: number;
  };
  recent_reviews_count: number;
}

export default function DashboardOverviewPage() {
  const { user } = useAuth();
  const { businesses, isLoading, refetch } = useBusinesses();
  const router = useRouter();
  const [selectedBusinessId, setSelectedBusinessId] = useState<string>('');
  const [businessStats, setBusinessStats] = useState<BusinessStats | null>(null);
  const [competitors, setCompetitors] = useState<CompetitorBusiness[]>([]);
  const [loadingStats, setLoadingStats] = useState(false);
  const [loadingCompetitors, setLoadingCompetitors] = useState(false);

  // Set default business when businesses load
  useEffect(() => {
    if (businesses.length > 0 && !selectedBusinessId) {
      setSelectedBusinessId(businesses[0].id);
    }
  }, [businesses, selectedBusinessId]);

  // Load business stats when business is selected
  useEffect(() => {
    if (selectedBusinessId) {
      loadBusinessStats(selectedBusinessId);
      loadCompetitors(selectedBusinessId);
    }
  }, [selectedBusinessId]);

  const loadBusinessStats = async (businessId: string) => {
    setLoadingStats(true);
    try {
      const business = businesses.find(b => b.id === businessId);
      if (!business) return;

      const response = await fetch(`/api/reviews/stats?google_place_id=${business.google_place_id}`);
      if (response.ok) {
        const stats = await response.json();
        setBusinessStats(stats);
      }
    } catch (error) {
      console.error('Error loading business stats:', error);
    } finally {
      setLoadingStats(false);
    }
  };

  const loadCompetitors = async (businessId: string) => {
    setLoadingCompetitors(true);
    try {
      const business = businesses.find(b => b.id === businessId);
      if (!business) return;

      const response = await fetch(`/api/competitors/list?business_id=${businessId}`);
      if (response.ok) {
        const data = await response.json();
        setCompetitors(data.competitors || []);
      }
    } catch (error) {
      console.error('Error loading competitors:', error);
    } finally {
      setLoadingCompetitors(false);
    }
  };

  const selectedBusiness = businesses.find(b => b.id === selectedBusinessId);

  console.log('Dashboard Overview Debug:', { isLoading, businessesLength: businesses.length, businesses });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (businesses.length === 0) {
    console.log('Redirecting to onboarding - no businesses found');
    // Redirect to the new streamlined onboarding page
    router.push('/onboarding');
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Redirecting to onboarding...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Dashboard Overview</h1>
          <p className="text-muted-foreground">
            Monitor your business performance and competitor insights
          </p>
        </div>

        {businesses.length > 1 && (
          <Select value={selectedBusinessId} onValueChange={setSelectedBusinessId}>
            <SelectTrigger className="w-[300px]">
              <SelectValue placeholder="Select a business" />
            </SelectTrigger>
            <SelectContent>
              {businesses.map((business) => (
                <SelectItem key={business.id} value={business.id}>
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    {business.business_name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {selectedBusiness && (
        <>
          {/* Business Info Card */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    {selectedBusiness.business_name}
                  </CardTitle>
                  <CardDescription className="flex items-center gap-2 mt-1">
                    <MapPin className="h-4 w-4" />
                    {selectedBusiness.business_address}
                  </CardDescription>
                </div>
                <Link href={`/dashboard/business/${selectedBusiness.id}`}>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </Link>
              </div>
            </CardHeader>
          </Card>

          {/* Stats Cards */}
          {businessStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Reviews</p>
                      <p className="text-2xl font-bold">{businessStats.total_reviews}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Average Rating</p>
                      <p className="text-2xl font-bold flex items-center gap-1">
                        {businessStats.average_rating.toFixed(1)}
                        <Star className="h-5 w-5 text-yellow-500 fill-current" />
                      </p>
                    </div>
                    <Star className="h-8 w-8 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Positive Reviews</p>
                      <p className="text-2xl font-bold text-green-600">
                        {businessStats.sentiment_distribution.positive}
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Recent Reviews</p>
                      <p className="text-2xl font-bold">{businessStats.recent_reviews_count}</p>
                      <p className="text-xs text-muted-foreground">Last 30 days</p>
                    </div>
                    <Calendar className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Main Content Tabs */}
          <Tabs defaultValue="competitors" className="space-y-4">
            <TabsList>
              <TabsTrigger value="competitors">Competitor Analysis</TabsTrigger>
              <TabsTrigger value="insights">Business Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="competitors" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Nearby Competitors
                  </CardTitle>
                  <CardDescription>
                    Top businesses in your area with their latest reviews
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loadingCompetitors ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                      <p>Loading competitor data...</p>
                    </div>
                  ) : competitors.length > 0 ? (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      {competitors.slice(0, 6).map((competitor) => (
                        <Card key={competitor.id} className="border-l-4 border-l-blue-500">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div>
                                <h4 className="font-medium">{competitor.name}</h4>
                                <p className="text-sm text-muted-foreground flex items-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  {competitor.address}
                                </p>
                              </div>
                              <div className="text-right">
                                <div className="flex items-center gap-1">
                                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                  <span className="font-medium">{competitor.rating.toFixed(1)}</span>
                                </div>
                                <p className="text-xs text-muted-foreground">
                                  {competitor.reviews_count} reviews
                                </p>
                              </div>
                            </div>

                            <Badge variant="secondary" className="mb-3">
                              {competitor.category}
                            </Badge>

                            {competitor.recent_reviews.length > 0 && (
                              <div className="space-y-2">
                                <p className="text-sm font-medium">Recent Reviews:</p>
                                {competitor.recent_reviews.slice(0, 2).map((review) => (
                                  <div key={review.id} className="text-xs bg-muted p-2 rounded">
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="flex">
                                        {[...Array(5)].map((_, i) => (
                                          <Star
                                            key={i}
                                            className={`h-3 w-3 ${
                                              i < review.rating
                                                ? 'text-yellow-500 fill-current'
                                                : 'text-gray-300'
                                            }`}
                                          />
                                        ))}
                                      </div>
                                      <Badge 
                                        variant={
                                          review.sentiment === 'positive' ? 'default' :
                                          review.sentiment === 'negative' ? 'destructive' : 'secondary'
                                        }
                                        className="text-xs"
                                      >
                                        {review.sentiment}
                                      </Badge>
                                    </div>
                                    <p className="line-clamp-2">
                                      {review.review_text.substring(0, 100)}...
                                    </p>
                                  </div>
                                ))}
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">
                        No competitor data available. Run competitor analysis to get insights.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Business Performance Insights</CardTitle>
                  <CardDescription>
                    Key metrics and trends for your business
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <TrendingUp className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">
                      Advanced insights coming soon. Visit the detailed business page for more analytics.
                    </p>
                    <Link href={`/dashboard/business/${selectedBusiness.id}`}>
                      <Button className="mt-4">
                        View Detailed Analytics
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}
