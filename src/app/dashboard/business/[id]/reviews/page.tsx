"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  Filter,
  Star,
  TrendingUp,
  BarChart3,
  Loader2,
  Calendar,
  MessageSquare,
} from "lucide-react";
import { apiClient } from "@/lib/api";
import type { BusinessInfo } from "@/types/api";
import { Review, useReviews } from "@/hooks/useReviews";
import { ReviewCard } from "@/components/ReviewCard";
import { SentimentChart } from "@/components/SentimentChart";
import { useBusinessTranslations } from '@/lib/i18n';

export default function AllReviewsPage() {
  const params = useParams();
  const router = useRouter();
  const businessId = params.id as string;
  const t = useBusinessTranslations();

  const [business, setBusiness] = useState<BusinessInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sentimentFilter, setSentimentFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("newest");

  // Get reviews data for this business
  const { data: reviewsData, isLoading: reviewsLoading } = useReviews(
    business?.google_place_id
  );

  useEffect(() => {
    const loadBusiness = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get all businesses and find the one with matching ID
        const businesses = await apiClient.getMyBusinesses();
        const foundBusiness = businesses.find(b => b.id === businessId);

        if (!foundBusiness) {
          setError("Business not found");
          return;
        }

        setBusiness(foundBusiness);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load business"
        );
      } finally {
        setIsLoading(false);
      }
    };

    if (businessId) {
      loadBusiness();
    }
  }, [businessId]);

  // Filter and sort reviews
  const getFilteredAndSortedReviews = () => {
    if (!reviewsData?.reviews) return [];

    let filtered = reviewsData.reviews;

    // Apply sentiment filter
    if (sentimentFilter !== "all") {
      filtered = filtered.filter(
        (review: Review) =>
          review.sentiment?.toLowerCase() === sentimentFilter.toLowerCase()
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.time).getTime() - new Date(a.time).getTime();
        case "oldest":
          return new Date(a.time).getTime() - new Date(b.time).getTime();
        case "highest-rating":
          return b.rating - a.rating;
        case "lowest-rating":
          return a.rating - b.rating;
        default:
          return 0;
      }
    });

    return sorted;
  };

  const filteredReviews = getFilteredAndSortedReviews();
  const allReviews = reviewsData?.reviews || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>Loading business details...</span>
      </div>
    );
  }

  if (error || !business) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('back')}
        </Button>

        <Alert variant="destructive">
          <AlertDescription>{error || t('businessNotFound')}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <Button
            variant="ghost"
            onClick={() => router.push(`/dashboard/business/${businessId}`)}
            className="mb-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('backToBusiness', { businessName: business.business_name })}
          </Button>
          <h1 className="text-2xl font-bold">{t('allReviews')}</h1>
          <p className="text-gray-600">
            {t('completeAnalysis', { businessName: business.business_name })}
          </p>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <MessageSquare className="h-8 w-8 mx-auto text-blue-600 mb-2" />
            <div className="text-2xl font-bold">{allReviews.length}</div>
            <div className="text-sm text-gray-600">{t('totalReviews')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Star className="h-8 w-8 mx-auto text-yellow-500 mb-2" />
            <div className="text-2xl font-bold">
              {reviewsData?.average_rating
                ? reviewsData.average_rating.toFixed(1)
                : "—"}
            </div>
            <div className="text-sm text-gray-600">{t('averageRating')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 mx-auto text-green-600 mb-2" />
            <div className="text-2xl font-bold text-green-600">
              {
                allReviews.filter(
                  (r: Review) => r.sentiment?.toLowerCase() === "positive"
                ).length
              }
            </div>
            <div className="text-sm text-gray-600">{t('positive')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <BarChart3 className="h-8 w-8 mx-auto text-red-600 mb-2" />
            <div className="text-2xl font-bold text-red-600">
              {
                allReviews.filter(
                  (r: Review) => r.sentiment?.toLowerCase() === "negative"
                ).length
              }
            </div>
            <div className="text-sm text-gray-600">{t('negative')}</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content - Reviews List */}
        <div className="lg:col-span-2 space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                {t('filtersAndSorting')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t('sentiment')}</label>
                  <Select
                    value={sentimentFilter}
                    onValueChange={setSentimentFilter}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t('allSentiments')}</SelectItem>
                      <SelectItem value="positive">{t('positive')}</SelectItem>
                      <SelectItem value="neutral">{t('neutral')}</SelectItem>
                      <SelectItem value="negative">{t('negative')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">{t('sortBy')}</label>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest">{t('newestFirst')}</SelectItem>
                      <SelectItem value="oldest">{t('oldestFirst')}</SelectItem>
                      <SelectItem value="highest-rating">
                        {t('highestRating')}
                      </SelectItem>
                      <SelectItem value="lowest-rating">
                        {t('lowestRating')}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="mt-4 text-sm text-gray-600">
                {t('showingResults', { filtered: filteredReviews.length, total: allReviews.length })}
              </div>
            </CardContent>
          </Card>

          {/* Reviews List */}
          <div className="space-y-4">
            {reviewsLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                {t('loadingReviews')}
              </div>
            ) : filteredReviews.length > 0 ? (
              filteredReviews.map(review => (
                <ReviewCard key={review.id} review={review} />
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">{t('noReviewsFound')}</h3>
                  <p className="text-gray-600">
                    {sentimentFilter !== "all"
                      ? t('noReviewsWithFilters', { sentiment: sentimentFilter })
                      : t('noReviewsAvailable')}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Sidebar - Analytics */}
        <div className="space-y-6">
          {/* Sentiment Analysis */}
          {allReviews.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  {t('sentimentAnalysis')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SentimentChart reviews={allReviews} />
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('quickActions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                className="w-full"
                onClick={() =>
                  router.push(`/dashboard/business/${businessId}/analytics`)
                }
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                {t('viewAnalytics')}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() =>
                  router.push(`/dashboard/business/${businessId}/summaries`)
                }
              >
                <Calendar className="h-4 w-4 mr-2" />
                {t('summaryReports')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
