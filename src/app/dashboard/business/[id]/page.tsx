"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  MapPin,
  Phone,
  Globe,
  Calendar,
  Star,
  TrendingUp,
  FileText,
  BarChart3,
  Loader2,
  ExternalLink,
  Download,
} from "lucide-react";
import { apiClient } from "@/lib/api";
import type { BusinessInfo, Review } from "@/types/api";
import { useSummaries } from "@/hooks/useSummaries";
import { useBusinessTranslations } from "@/lib/i18n";
import { ReviewCard } from "@/components/ReviewCard";
import { SentimentChart } from "@/components/SentimentChart";
import { BusinessHeader } from "@/components/BusinessHeader";
import { Pagination, PaginationInfo } from "@/components/Pagination";
import { usePaginatedReviews } from "@/hooks/usePaginatedReviews";
import { useRateLimit } from "@/hooks/useRateLimit";
import { useFetchReviews } from "@/hooks/useFetchReviews";
export default function BusinessDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const businessId = params.id as string;

  const [business, setBusiness] = useState<BusinessInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const t = useBusinessTranslations();

  // Extract google_place_id to prevent unnecessary re-renders
  // Use useMemo to ensure stable reference and prevent infinite loops
  const googlePlaceId = useMemo(() => business?.google_place_id, [business?.google_place_id]);

  // Get paginated reviews data for this business
  const {
    reviews: paginatedReviews,
    loading: reviewsLoading,
    currentPage,
    totalPages,
    total,
    hasNext,
    hasPrevious,
    averageRating,
    goToPage,
  } = usePaginatedReviews({
    filters: { google_place_id: googlePlaceId },
    limit: 10,
    autoFetch: !!googlePlaceId,
  });

  // Get rate limit status for this business
  const {
    canFetch,
    message: rateLimitMessage,
    timeUntilNextFetch,
  } = useRateLimit(googlePlaceId);

  // Mutation for fetching reviews
  const fetchReviewsMutation = useFetchReviews();

  // Get summaries data for this business
  const { data: summariesData, isLoading: summariesLoading } = useSummaries();

  useEffect(() => {
    const loadBusiness = async () => {
      try {
        setIsLoading(true);
        setError(null);
        setBusiness(null); // Clear previous business data

        // Get all businesses and find the one with matching ID
        const businesses = await apiClient.getMyBusinesses();
        const foundBusiness = businesses.find(b => b.id === businessId);

        if (!foundBusiness) {
          setError("Business not found");
          return;
        }

        setBusiness(foundBusiness);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load business"
        );
      } finally {
        setIsLoading(false);
      }
    };

    if (businessId) {
      loadBusiness();
    }
  }, [businessId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>{t("loadingBusinessDetails")}</span>
      </div>
    );
  }

  if (error || !business) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("back")}
        </Button>

        <Alert variant="destructive">
          <AlertDescription>{error || t("businessNotFound")}</AlertDescription>
        </Alert>
      </div>
    );
  }

  const reviews = paginatedReviews || [];
  const latestReviews = reviews.slice(0, 5);
  const summaries = summariesData || [];
  const latestSummary = summaries[0];

  return (
    <div key={businessId} className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => router.push("/dashboard")}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("backToBusinesses")}
        </Button>
      </div>

      {/* Business Header */}
      <BusinessHeader
        business={business}
        reviewsData={{ reviews, total, average_rating: averageRating || 0 }}
      />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Latest Reviews */}
        <div className="lg:col-span-2 space-y-6">
          {/* Latest Reviews Section */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                {t("latestReviews")} (Last 3 Months)
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (googlePlaceId && business) {
                      fetchReviewsMutation.mutate(
                        {
                          google_place_id: googlePlaceId,
                          business_name: business.business_name,
                        },
                        {
                          onSuccess: data => {
                            alert(
                              `Successfully fetched ${data.total_reviews} reviews for ${data.business_name}`
                            );
                          },
                          onError: error => {
                            alert(error.message || "Failed to fetch reviews");
                          },
                        }
                      );
                    }
                  }}
                  disabled={!canFetch || fetchReviewsMutation.isPending}
                  title={
                    !canFetch ? rateLimitMessage : t("updateReviewsTooltip")
                  }
                >
                  {fetchReviewsMutation.isPending ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4 mr-2" />
                  )}
                  {fetchReviewsMutation.isPending
                    ? t("updatingReviews")
                    : t("updateReviews")}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    router.push(`/dashboard/business/${businessId}/reviews`)
                  }
                >
                  {t("viewAllReviews")}
                  <ExternalLink className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {reviewsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  {t("loadingReviews")}
                </div>
              ) : latestReviews.length > 0 ? (
                latestReviews.map((review: Review) => (
                  <ReviewCard key={review.id} review={review} />
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  {t("noReviewsYet")}
                </div>
              )}

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="border-t pt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                    hasNext={hasNext}
                    hasPrevious={hasPrevious}
                  />
                  <PaginationInfo
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={total}
                    itemsPerPage={10}
                    className="mt-2 text-center"
                  />
                </div>
              )}

              {/* Rate Limit Info */}
              {!canFetch && rateLimitMessage && (
                <div className="border-t pt-4">
                  <Alert>
                    <AlertDescription className="text-sm">
                      {rateLimitMessage}
                      {timeUntilNextFetch && (
                        <span className="block mt-1 font-medium">
                          {t("nextUpdateAvailable", {
                            time: timeUntilNextFetch,
                          })}
                        </span>
                      )}
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Sentiment Analysis Chart */}
          {reviews.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  {t("sentimentAnalysis")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SentimentChart reviews={reviews} />
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Summary & Actions */}
        <div className="space-y-6">
          {/* Latest Summary Report */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {t("latestSummaryReport")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {summariesLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {t("loadingReviews")}
                </div>
              ) : latestSummary ? (
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    {t("generatedOn", {
                      date: formatDate(latestSummary.created_at),
                    })}
                  </div>
                  <p className="text-sm line-clamp-3">
                    {latestSummary.positive_themes.slice(0, 3).join(', ')}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() =>
                      router.push(`/dashboard/business/${businessId}/summaries`)
                    }
                  >
                    {t("viewAllReports")}
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  {t("noSummaryReports")}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t("quickActions")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                className="w-full"
                onClick={() =>
                  router.push(`/dashboard/business/${businessId}/analytics`)
                }
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                {t("viewAnalytics")}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() =>
                  router.push(`/dashboard/business/${businessId}/summaries`)
                }
              >
                <FileText className="h-4 w-4 mr-2" />
                {t("summaryReports")}
              </Button>
            </CardContent>
          </Card>

          {/* Business Info Card */}
          <Card>
            <CardHeader>
              <CardTitle>{t("businessInformation")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {business.business_address && (
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 mt-1 text-gray-500 flex-shrink-0" />
                  <span className="text-sm break-words">
                    {business.business_address}
                  </span>
                </div>
              )}

              {business.phone_number && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{business.phone_number}</span>
                </div>
              )}

              {business.website && (
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-gray-500" />
                  <a
                    href={business.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    {t("visitWebsite")}
                  </a>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {t("addedOn", { date: formatDate(business.created_at) })}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
