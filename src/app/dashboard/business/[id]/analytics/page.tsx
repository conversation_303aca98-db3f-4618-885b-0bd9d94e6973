"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  TrendingUp,
  BarChart3,
  Star,
  MessageSquare,
  Calendar,
  Loader2,
} from "lucide-react";
import { apiClient, type BusinessInfo } from "@/lib/api";
import { useReviews, Review } from "@/hooks/useReviews";
import { useBusinessTranslations } from "@/lib/i18n";

export default function AnalyticsPage() {
  const params = useParams();
  const router = useRouter();
  const businessId = params.id as string;

  const [business, setBusiness] = useState<BusinessInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const t = useBusinessTranslations();

  // Get reviews data for this business
  const { data: reviewsData } = useReviews(business?.google_place_id);

  useEffect(() => {
    const loadBusiness = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get all businesses and find the one with matching ID
        const businesses = await apiClient.getMyBusinesses();
        const foundBusiness = businesses.find(b => b.id === businessId);

        if (!foundBusiness) {
          setError("Business not found");
          return;
        }

        setBusiness(foundBusiness);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load business"
        );
      } finally {
        setIsLoading(false);
      }
    };

    if (businessId) {
      loadBusiness();
    }
  }, [businessId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>{t("loadingBusinessDetails")}</span>
      </div>
    );
  }

  if (error || !business) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        <Alert variant="destructive">
          <AlertDescription>{error || t("businessNotFound")}</AlertDescription>
        </Alert>
      </div>
    );
  }

  const reviews = reviewsData?.reviews || [];

  // Calculate analytics
  const totalReviews = reviews.length;
  const averageRating = reviewsData?.average_rating || 0;
  const positiveReviews = reviews.filter(
    (r: Review) => r.sentiment?.toLowerCase() === "positive"
  ).length;
  const negativeReviews = reviews.filter(
    (r: Review) => r.sentiment?.toLowerCase() === "negative"
  ).length;
  const neutralReviews = reviews.filter(
    (r: Review) => r.sentiment?.toLowerCase() === "neutral"
  ).length;

  // Rating distribution
  const ratingDistribution = [1, 2, 3, 4, 5].map(rating => ({
    rating,
    count: reviews.filter((r: Review) => r.rating === rating).length,
    percentage:
      totalReviews > 0
        ? (reviews.filter((r: Review) => r.rating === rating).length /
            totalReviews) *
          100
        : 0,
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <Button
            variant="ghost"
            onClick={() => router.push(`/dashboard/business/${businessId}`)}
            className="mb-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to {business.business_name}
          </Button>
          <h1 className="text-2xl font-bold">{t("viewAnalytics")}</h1>
          <p className="text-gray-600">
            Detailed analytics and insights for {business.business_name}
          </p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6 text-center">
            <MessageSquare className="h-8 w-8 mx-auto text-blue-600 mb-2" />
            <div className="text-3xl font-bold text-gray-900">
              {totalReviews}
            </div>
            <div className="text-sm text-gray-600">Total Reviews</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <Star className="h-8 w-8 mx-auto text-yellow-500 mb-2" />
            <div className="text-3xl font-bold text-gray-900">
              {averageRating > 0 ? averageRating.toFixed(1) : "—"}
            </div>
            <div className="text-sm text-gray-600">Average Rating</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <TrendingUp className="h-8 w-8 mx-auto text-green-600 mb-2" />
            <div className="text-3xl font-bold text-green-600">
              {positiveReviews}
            </div>
            <div className="text-sm text-gray-600">Positive Reviews</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-8 w-8 mx-auto text-red-600 mb-2" />
            <div className="text-3xl font-bold text-red-600">
              {negativeReviews}
            </div>
            <div className="text-sm text-gray-600">Negative Reviews</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sentiment Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {t("sentimentAnalysis")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-green-700">
                    {t("positive")}
                  </span>
                  <span className="text-sm text-gray-600">
                    {positiveReviews} (
                    {totalReviews > 0
                      ? ((positiveReviews / totalReviews) * 100).toFixed(1)
                      : 0}
                    %)
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-green-500 h-3 rounded-full transition-all duration-300"
                    style={{
                      width: `${totalReviews > 0 ? (positiveReviews / totalReviews) * 100 : 0}%`,
                    }}
                  />
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">
                    {t("neutral")}
                  </span>
                  <span className="text-sm text-gray-600">
                    {neutralReviews} (
                    {totalReviews > 0
                      ? ((neutralReviews / totalReviews) * 100).toFixed(1)
                      : 0}
                    %)
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gray-500 h-3 rounded-full transition-all duration-300"
                    style={{
                      width: `${totalReviews > 0 ? (neutralReviews / totalReviews) * 100 : 0}%`,
                    }}
                  />
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-red-700">
                    {t("negative")}
                  </span>
                  <span className="text-sm text-gray-600">
                    {negativeReviews} (
                    {totalReviews > 0
                      ? ((negativeReviews / totalReviews) * 100).toFixed(1)
                      : 0}
                    %)
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-red-500 h-3 rounded-full transition-all duration-300"
                    style={{
                      width: `${totalReviews > 0 ? (negativeReviews / totalReviews) * 100 : 0}%`,
                    }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Rating Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Rating Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {ratingDistribution
                .reverse()
                .map(({ rating, count, percentage }) => (
                  <div key={rating} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{rating}</span>
                        <div className="flex">
                          {Array.from({ length: 5 }, (_, i) => (
                            <Star
                              key={i}
                              className={`h-3 w-3 ${
                                i < rating
                                  ? "text-yellow-400 fill-current"
                                  : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <span className="text-sm text-gray-600">
                        {count} ({percentage.toFixed(1)}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>{t("quickActions")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="w-full"
              onClick={() =>
                router.push(`/dashboard/business/${businessId}/reviews`)
              }
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              {t("viewAllReviews")}
            </Button>
            <Button
              variant="outline"
              className="w-full"
              onClick={() =>
                router.push(`/dashboard/business/${businessId}/summaries`)
              }
            >
              <Calendar className="h-4 w-4 mr-2" />
              {t("summaryReports")}
            </Button>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push(`/dashboard/business/${businessId}`)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("businessDetails")}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
