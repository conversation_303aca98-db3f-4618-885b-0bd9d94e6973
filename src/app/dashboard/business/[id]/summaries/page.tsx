"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  FileText,
  Calendar,
  TrendingUp,
  Loader2,
  Download,
} from "lucide-react";
import { apiClient, BusinessInfo } from "@/lib/api";
import { Summary, useSummaries } from "@/hooks/useSummaries";
import { useBusinessTranslations } from "@/lib/i18n";

export default function SummaryReportsPage() {
  const params = useParams();
  const router = useRouter();
  const businessId = params.id as string;

  const [business, setBusiness] = useState<BusinessInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const t = useBusinessTranslations();

  // Get summaries data for this business
  const { data: summariesData, isLoading: summariesLoading } = useSummaries(
    business?.google_place_id
  );

  useEffect(() => {
    const loadBusiness = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get all businesses and find the one with matching ID
        const businesses = await apiClient.getMyBusinesses();
        const foundBusiness = businesses.find(b => b.id === businessId);

        if (!foundBusiness) {
          setError("Business not found");
          return;
        }

        setBusiness(foundBusiness);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load business"
        );
      } finally {
        setIsLoading(false);
      }
    };

    if (businessId) {
      loadBusiness();
    }
  }, [businessId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>{t("loadingBusinessDetails")}</span>
      </div>
    );
  }

  if (error || !business) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        <Alert variant="destructive">
          <AlertDescription>{error || t("businessNotFound")}</AlertDescription>
        </Alert>
      </div>
    );
  }

  const summaries = summariesData || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <Button
            variant="ghost"
            onClick={() => router.push(`/dashboard/business/${businessId}`)}
            className="mb-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to {business.business_name}
          </Button>
          <h1 className="text-2xl font-bold">{t("summaryReports")}</h1>
          <p className="text-gray-600">
            AI-generated insights and analysis for {business.business_name}
          </p>
        </div>

        <Button>
          <Download className="h-4 w-4 mr-2" />
          Generate New Report
        </Button>
      </div>

      {/* Summary Reports List */}
      <div className="space-y-4">
        {summariesLoading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading summary reports...
            </CardContent>
          </Card>
        ) : summaries.length > 0 ? (
          summaries.map((summary: Summary) => (
            <Card
              key={summary.id}
              className="hover:shadow-md transition-shadow"
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Summary Report
                    </CardTitle>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>
                          {t("generatedOn")} {formatDate(summary.created_at)}
                        </span>
                      </div>
                      <Badge variant="outline">
                        {summary.summary_type || "General"}
                      </Badge>
                    </div>
                  </div>

                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  {/* Summary Text */}
                  <div className="prose prose-sm max-w-none">
                    <p className="text-gray-700 leading-relaxed">
                      {summary.summary_text}
                    </p>
                  </div>

                  {/* Period Information */}
                  {summary.period_start && summary.period_end && (
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="text-sm text-gray-600">
                        <strong>Analysis Period:</strong>{" "}
                        {formatDate(summary.period_start)} -{" "}
                        {formatDate(summary.period_end)}
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2 border-t border-gray-200">
                    <Button variant="outline" size="sm">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      View Analytics
                    </Button>
                    <Button variant="outline" size="sm">
                      <FileText className="h-4 w-4 mr-2" />
                      Full Report
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="h-16 w-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Summary Reports</h3>
              <p className="text-gray-600 mb-6">{t("noSummaryReports")}</p>
              <Button>
                <Download className="h-4 w-4 mr-2" />
                Generate First Report
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
