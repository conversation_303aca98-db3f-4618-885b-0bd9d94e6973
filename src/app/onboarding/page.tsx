'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Building2, User } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useBusinesses } from '@/hooks/useBusinesses';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface OnboardingFormData {
  fullName: string;
  businessRole: string;
  googleMapsUrl: string;
  businessDescription: string;
}




export default function OnboardingPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { businesses, isLoading: businessesLoading } = useBusinesses();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<OnboardingFormData>({
    fullName: '',
    businessRole: '',
    googleMapsUrl: '',
    businessDescription: '',
  });

  // Redirect to dashboard if user already has businesses
  useEffect(() => {
    if (!businessesLoading && businesses.length > 0) {
      console.log('User already has businesses, redirecting to dashboard');
      router.push('/dashboard');
    }
  }, [businesses, businessesLoading, router]);

  const isFormValid = formData.fullName.trim().length > 0 && 
                     formData.businessRole.trim().length > 0 && 
                     (formData.googleMapsUrl.includes('google.com/maps') || formData.googleMapsUrl.includes('goo.gl'));

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !isFormValid) return;

    setIsLoading(true);
    try {
      // Simultaneous API calls for user profile and business setup
      const [, businessSearchResult] = await Promise.all([
        // Create/update user profile
        apiClient.post('/api/user/profile', {
          full_name: formData.fullName,
          business_role: formData.businessRole,
          business_description: formData.businessDescription,
        }),
        
        // Search for business information
        apiClient.searchBusinessFromUrl(formData.googleMapsUrl)
      ]);

      // Save business to user profile
      await apiClient.saveBusiness({
        place_id: businessSearchResult.place_id,
        name: businessSearchResult.name,
        address: businessSearchResult.address,
        phone: businessSearchResult.phone,
        website: businessSearchResult.website,
        rating: businessSearchResult.rating,
        total_reviews: businessSearchResult.total_reviews,
        types: businessSearchResult.types,
      });

      toast.success('Profile and business setup completed successfully!');

      // Always redirect to dashboard after successful onboarding
      // The dashboard will handle routing to the appropriate page
      router.push('/dashboard');
    } catch (error) {
      console.error('Onboarding error:', error);
      toast.error(error instanceof Error ? error.message : 'Setup failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof OnboardingFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Show loading while checking authentication and businesses
  if (!user || businessesLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p>{!user ? 'Redirecting to login...' : 'Checking your account...'}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If user has businesses, they shouldn't see this page (redirect will happen via useEffect)
  if (businesses.length > 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p>Redirecting to dashboard...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-6 w-6" />
            Welcome to Review Pulse
          </CardTitle>
          <CardDescription>
            Let's get your profile and business set up quickly
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* User Information Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <User className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">Your Information</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fullName">Full Name *</Label>
                  <Input
                    id="fullName"
                    value={formData.fullName}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    placeholder="Enter your full name"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="businessRole">Your Role *</Label>
                  <Input
                    id="businessRole"
                    value={formData.businessRole}
                    onChange={(e) => handleInputChange('businessRole', e.target.value)}
                    placeholder="Owner, Manager, etc."
                    required
                  />
                </div>
              </div>
            </div>

            {/* Business Information Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Building2 className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">Your Business</h3>
              </div>
              
              <div>
                <Label htmlFor="googleMapsUrl">Google Maps URL *</Label>
                <Input
                  id="googleMapsUrl"
                  value={formData.googleMapsUrl}
                  onChange={(e) => handleInputChange('googleMapsUrl', e.target.value)}
                  placeholder="https://maps.google.com/..."
                  required
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Copy the URL from your Google Maps business listing
                </p>
              </div>
              
              <div>
                <Label htmlFor="businessDescription">Business Description (Optional)</Label>
                <Textarea
                  id="businessDescription"
                  value={formData.businessDescription}
                  onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                  placeholder="Brief description of your business"
                  rows={3}
                />
              </div>
            </div>

            <Button 
              type="submit"
              disabled={!isFormValid || isLoading}
              className="w-full"
            >
              {isLoading ? 'Setting up your account...' : 'Complete Setup'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
