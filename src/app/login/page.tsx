"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { InputField } from "@/components/ui/form-field";
import { loginSchema, type LoginFormData } from "@/lib/validations";
import { H2, Text } from "@/components/ui/typography";
import { useAuthTranslations, useFormTranslations } from "@/lib/i18n";
import { GlobalHeader } from "@/components/GlobalHeader";

export default function LoginPage() {
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const { signIn } = useAuth();
  const router = useRouter();
  const t = useAuthTranslations();
  const tf = useFormTranslations();

  const handleSubmit = async (data: LoginFormData) => {
    setError("");
    setLoading(true);

    try {
      const { error } = await signIn(data.email, data.password);

      if (error) {
        setError(error.message);
      } else {
        router.push("/dashboard");
      }
    } catch {
      setError(t("unexpectedError"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <GlobalHeader />
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <H2>{t("signInTitle")}</H2>
          </div>

        <FormWrapper
          schema={loginSchema}
          onSubmit={handleSubmit}
          error={error}
          isLoading={loading}
          submitText={loading ? tf("buttons.signingIn") : tf("buttons.signIn")}
          showCard
          className="mt-8"
        >
          <InputField
            name="email"
            type="email"
            label={tf("labels.email")}
            placeholder={tf("placeholders.email")}
            autoComplete="email"
            required
          />

          <InputField
            name="password"
            type="password"
            label={tf("labels.password")}
            placeholder={tf("placeholders.password")}
            autoComplete="current-password"
            required
          />
        </FormWrapper>

        <div className="text-center">
          <Text className="text-sm">
            {t("noAccount")}{" "}
            <Link
              href="/register"
              className="font-medium text-primary hover:text-primary/80 transition-colors"
            >
              {t("signUpLink")}
            </Link>
          </Text>
        </div>
      </div>
    </div>
    </div>
  );
}
