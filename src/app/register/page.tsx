"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { InputField } from "@/components/ui/form-field";
import { registerSchema, type RegisterFormData } from "@/lib/validations";
import { H2, Text } from "@/components/ui/typography";
import { CheckCircle } from "lucide-react";
import { useAuthTranslations, useFormTranslations } from "@/lib/i18n";
import { GlobalHeader } from "@/components/GlobalHeader";

export default function RegisterPage() {
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const { signUp } = useAuth();
  const router = useRouter();
  const t = useAuthTranslations();
  const tf = useFormTranslations();

  const handleSubmit = async (data: RegisterFormData) => {
    setError("");
    setLoading(true);

    try {
      const { error } = await signUp(data.email, data.password);

      if (error) {
        setError(error.message);
      } else {
        setSuccess(true);
        setTimeout(() => {
          router.push("/login");
        }, 2000);
      }
    } catch {
      setError(t("unexpectedError"));
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <H2>{t("checkEmailTitle")}</H2>
            <Text className="text-muted-foreground">
              {t("checkEmailMessage")}
            </Text>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <GlobalHeader />
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <H2>{t("signUpTitle")}</H2>
          </div>

        <FormWrapper
          schema={registerSchema}
          onSubmit={handleSubmit}
          error={error}
          isLoading={loading}
          submitText={
            loading
              ? tf("buttons.creatingAccount")
              : tf("buttons.createAccount")
          }
          showCard
          className="mt-8"
        >
          <InputField
            name="email"
            type="email"
            label={tf("labels.email")}
            placeholder={tf("placeholders.email")}
            autoComplete="email"
            required
          />

          <InputField
            name="password"
            type="password"
            label={tf("labels.password")}
            placeholder={tf("placeholders.password")}
            autoComplete="new-password"
            description={tf("descriptions.password")}
            required
          />

          <InputField
            name="confirmPassword"
            type="password"
            label={tf("labels.confirmPassword")}
            placeholder={tf("placeholders.confirmPassword")}
            autoComplete="new-password"
            required
          />
        </FormWrapper>

        <div className="text-center">
          <Text className="text-sm">
            {t("hasAccount")}{" "}
            <Link
              href="/login"
              className="font-medium text-primary hover:text-primary/80 transition-colors"
            >
              {t("signInLink")}
            </Link>
          </Text>
        </div>
        </div>
      </div>
    </div>
  );
}
