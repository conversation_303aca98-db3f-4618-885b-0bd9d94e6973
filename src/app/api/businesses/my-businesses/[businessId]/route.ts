import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { BusinessService } from '@/lib/services/business';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { businessId: string } }
) {
  try {
    // Authenticate user
    const user = await requireAuth(request);

    const { businessId } = params;

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      );
    }

    // Get Apify API token from environment
    const apifyApiToken = process.env.APIFY_API_TOKEN;
    if (!apifyApiToken) {
      return NextResponse.json(
        { error: 'Apify API token not configured' },
        { status: 500 }
      );
    }

    // Initialize business service
    const businessService = new BusinessService(apifyApiToken);

    // Remove business
    const result = await businessService.removeBusinessProfile(businessId, user);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error removing business:', error);

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
