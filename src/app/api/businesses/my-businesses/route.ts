import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { BusinessService } from '@/lib/services/business';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireAuth(request);
    console.log({user})

    // Get Apify API token from environment
    const apifyApiToken = process.env.APIFY_API_TOKEN;
    if (!apifyApiToken) {
      console.error('Apify API token not configured');
      return NextResponse.json(
        { error: 'Apify API token not configured' },
        { status: 500 }
      );
    }

    // Initialize business service
    const businessService = new BusinessService(apifyApiToken);

    // Get user businesses
    const businesses = await businessService.getUserBusinesses(user);
    console.log('Businesses received in API:', businesses);

    return NextResponse.json(businesses);
  } catch (error) {
    console.error('Error fetching user businesses:', error);

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
