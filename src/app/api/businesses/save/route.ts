import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { BusinessService } from '@/lib/services/business';

const saveBusinessSchema = z.object({
  place_id: z.string().min(1, 'Place ID is required'),
  name: z.string().min(1, 'Business name is required'),
  address: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional(),
  rating: z.number().optional(),
  total_reviews: z.number().optional(),
  types: z.array(z.string()).optional(),
  original_search_url: z.string().optional(),
  trigger_six_month_count: z.boolean().default(false),
});

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireAuth(request);

    // Parse and validate request body
    const body = await request.json();
    const businessData = saveBusinessSchema.parse(body);

    // Get Apify API token from environment
    const apifyApiToken = process.env.APIFY_API_TOKEN;
    if (!apifyApiToken) {
      return NextResponse.json(
        { error: 'Apify API token not configured' },
        { status: 500 }
      );
    }

    // Initialize business service
    const businessService = new BusinessService(apifyApiToken);

    // Save business with original query/URL information
    const result = await businessService.saveBusiness(businessData, user);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error saving business:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
