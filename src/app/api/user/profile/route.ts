import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-server';

// GET /api/user/profile - Get user profile
export async function GET(request: NextRequest) {
  try {
    const { user, supabaseAdmin } = await authenticateUser(request);
    
    if (!user || !supabaseAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile using the authenticated supabase client
    const { data: profile, error } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching user profile:', error);
      return NextResponse.json({ error: 'Failed to fetch profile' }, { status: 500 });
    }

    return NextResponse.json({ profile: profile || null });
  } catch (error) {
    console.error('Error in GET /api/user/profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/user/profile - Create or update user profile
export async function POST(request: NextRequest) {
  try {
    const { user, supabaseAdmin } = await authenticateUser(request);
    
    if (!user || !supabaseAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { full_name, business_role, business_description, onboarding_completed } = body;

    // Upsert user profile using the authenticated supabase client
    const { data: profile, error } = await supabaseAdmin
      .from('user_profiles')
      .upsert({
        user_id: user.id,
        full_name,
        business_role,
        business_description,
        onboarding_completed: onboarding_completed ?? false,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id'
      })
      .select()
      .single();

    if (error) {
      console.error('Error upserting user profile:', error);
      return NextResponse.json({ error: 'Failed to save profile' }, { status: 500 });
    }

    return NextResponse.json({ profile });
  } catch (error) {
    console.error('Error in POST /api/user/profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}