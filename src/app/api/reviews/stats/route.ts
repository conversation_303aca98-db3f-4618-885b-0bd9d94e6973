import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { ReviewService } from '@/lib/services/reviews';

const statsSchema = z.object({
  google_place_id: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  include_all_reviews: z.boolean().default(false),
});

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireAuth(request);

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      google_place_id: searchParams.get('google_place_id') || undefined,
      start_date: searchParams.get('start_date') || undefined,
      end_date: searchParams.get('end_date') || undefined,
      include_all_reviews: searchParams.get('include_all_reviews') === 'true',
    };

    // Validate parameters
    const validatedParams = statsSchema.parse(queryParams);

    // Get Apify API token from environment
    const apifyApiToken = process.env.APIFY_API_TOKEN;
    if (!apifyApiToken) {
      return NextResponse.json(
        { error: 'Apify API token not configured' },
        { status: 500 }
      );
    }

    // Initialize review service
    const reviewService = new ReviewService(apifyApiToken);

    // Get review statistics
    const stats = await reviewService.getReviewStats(user, validatedParams);

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching review stats:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request parameters', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
