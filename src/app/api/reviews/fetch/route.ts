import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { ReviewService } from '@/lib/services/reviews';
import { SentimentAnalysisService } from '@/lib/services/sentiment';
import { DatabaseService } from '@/lib/services/database';

const fetchReviewsSchema = z.object({
  google_place_id: z.string().min(1, 'Place ID is required'),
  business_name: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireAuth(request);

    // Parse and validate request body
    const body = await request.json();
    const { google_place_id, business_name } = fetchReviewsSchema.parse(body);

    // Get configuration from environment
    const apifyApiToken = process.env.APIFY_API_TOKEN;
    const rateLimitSeconds = parseInt(process.env.RATE_LIMIT_SECONDS || '0');

    if (!apifyApiToken) {
      return NextResponse.json(
        { error: 'Apify API token not configured' },
        { status: 500 }
      );
    }

    // Initialize services
    const reviewService = new ReviewService(apifyApiToken);
    
    // Initialize sentiment analysis service
    let sentimentService: SentimentAnalysisService | null = null;
    try {
      const azureEndpoint = process.env.AZURE_TEXT_ANALYTICS_ENDPOINT;
      const azureKey = process.env.AZURE_TEXT_ANALYTICS_KEY;
      const geminiKey = process.env.GEMINI_API_KEY;

      if (azureEndpoint && azureKey) {
        sentimentService = new SentimentAnalysisService({
          azure: { endpoint: azureEndpoint, key: azureKey }
        });
      } else if (geminiKey) {
        sentimentService = new SentimentAnalysisService({
          ai: { provider: 'google', apiKey: geminiKey }
        });
      }
    } catch (error) {
      console.warn('Could not initialize sentiment analysis service:', error);
    }

    // Fetch reviews
    const result = await reviewService.fetchReviews(
      google_place_id,
      user,
      business_name,
      rateLimitSeconds
    );

    // Analyze sentiment for new reviews if service is available
    if (sentimentService && result.new_reviews > 0) {
      try {
        console.log(`Analyzing sentiment for ${result.new_reviews} new reviews`);
        
        const databaseService = new DatabaseService(true);
        
        // Get reviews that don't have sentiment yet
        const { reviews } = await databaseService.getReviewsForPlace(
          user.id,
          google_place_id,
          { includeAllReviews: true }
        );

        const reviewsWithoutSentiment = reviews.filter(review => !review.sentiment);
        
        for (const review of reviewsWithoutSentiment) {
          try {
            const sentimentResult = await sentimentService.analyzeSentiment(review.review_text);
            await databaseService.updateReviewSentiment(review.id, sentimentResult.sentiment);
            console.log(`Updated sentiment for review ${review.id}: ${sentimentResult.sentiment}`);
          } catch (sentimentError) {
            console.error(`Error analyzing sentiment for review ${review.id}:`, sentimentError);
          }
        }
      } catch (sentimentError) {
        console.error('Error in sentiment analysis:', sentimentError);
        // Don't fail the entire request if sentiment analysis fails
      }
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching reviews:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check for rate limiting error
      if (error.message.includes('Next update available in')) {
        return NextResponse.json(
          { error: error.message },
          { status: 429 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
