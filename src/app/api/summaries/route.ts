import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { SummarizationService } from '@/lib/services/summarization';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireAuth(request);

    // Get AI configuration from environment
    const geminiApiKey = process.env.GEMINI_API_KEY;
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const anthropicApiKey = process.env.ANTHROPIC_API_KEY;

    let aiConfig;
    if (geminiApiKey) {
      aiConfig = { provider: 'google' as const, apiKey: geminiApiKey };
    } else if (openaiApiKey) {
      aiConfig = { provider: 'openai' as const, apiKey: openaiApiKey };
    } else if (anthropicApiKey) {
      aiConfig = { provider: 'anthropic' as const, apiKey: anthropicApiKey };
    } else {
      return NextResponse.json(
        { error: 'No AI service configured' },
        { status: 500 }
      );
    }

    // Initialize summarization service
    const summarizationService = new SummarizationService(aiConfig);

    // Get user summaries
    const summaries = await summarizationService.getUserSummaries(user);

    return NextResponse.json(summaries);
  } catch (error) {
    console.error('Error fetching summaries:', error);

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
