import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { SummarizationService } from '@/lib/services/summarization';
import { DatabaseService } from '@/lib/services/database';

const generateSummarySchema = z.object({
  google_place_id: z.string().min(1, 'Place ID is required'),
  period_start: z.string(),
  period_end: z.string(),
  business_name: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireAuth(request);

    // Parse and validate request body
    const body = await request.json();
    const { google_place_id, period_start, period_end, business_name } = generateSummarySchema.parse(body);

    // Get AI configuration from environment
    const geminiApiKey = process.env.GEMINI_API_KEY;
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const anthropicApiKey = process.env.ANTHROPIC_API_KEY;

    let aiConfig;
    if (geminiApiKey) {
      aiConfig = { provider: 'google' as const, apiKey: geminiApiKey };
    } else if (openaiApiKey) {
      aiConfig = { provider: 'openai' as const, apiKey: openaiApiKey };
    } else if (anthropicApiKey) {
      aiConfig = { provider: 'anthropic' as const, apiKey: anthropicApiKey };
    } else {
      return NextResponse.json(
        { error: 'No AI service configured' },
        { status: 500 }
      );
    }

    // Initialize services
    const summarizationService = new SummarizationService(aiConfig);
    const databaseService = new DatabaseService(true);

    // Get reviews for the specified period
    const { reviews } = await databaseService.getReviewsForPlace(
      user.id,
      google_place_id,
      {
        startDate: period_start,
        endDate: period_end,
        includeAllReviews: true,
      }
    );

    // Generate summary
    const summaryResult = await summarizationService.generateSummary({
      reviews,
      periodStart: period_start,
      periodEnd: period_end,
      businessName: business_name,
    });

    // Save summary to database
    const savedSummary = await summarizationService.saveSummary(
      user,
      summaryResult,
      period_start,
      period_end
    );

    return NextResponse.json({
      summary: savedSummary,
      details: summaryResult,
    });
  } catch (error) {
    console.error('Error generating summary:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
