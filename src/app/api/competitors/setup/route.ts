import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { ApifyService } from '@/lib/services/apify';
import { DatabaseService } from '@/lib/services/database';
import { ReviewService } from '@/lib/services/reviews';

const setupCompetitorsSchema = z.object({
  business_location: z.string().min(1, 'Business location is required'),
  business_category: z.string().optional(),
  exclude_place_id: z.string().optional(),
  limit: z.number().min(1).max(20).default(15),
});

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireAuth(request);

    // Parse and validate request body
    const body = await request.json();
    const { business_location, business_category, exclude_place_id, limit } = setupCompetitorsSchema.parse(body);

    // Get configuration from environment
    const apifyApiToken = process.env.APIFY_API_TOKEN;
    if (!apifyApiToken) {
      return NextResponse.json(
        { error: 'Apify API token not configured' },
        { status: 500 }
      );
    }

    // Initialize services
    const apifyService = new ApifyService(apifyApiToken);
    const databaseService = new DatabaseService(true);
    const reviewService = new ReviewService(apifyApiToken);

    console.log(`Setting up competitors for location: ${business_location}, category: ${business_category}`);

    // Step 1: Search for competitor businesses
    const competitors = await apifyService.searchBusinessesByLocation({
      location: business_location,
      category: business_category,
      limit: limit + 5, // Get a few extra in case some fail
    });

    if (!competitors || competitors.length === 0) {
      return NextResponse.json(
        { error: 'No competitor businesses found in the specified location' },
        { status: 404 }
      );
    }

    // Filter out the user's own business if specified
    const filteredCompetitors = exclude_place_id
      ? competitors.filter(business => business.placeId !== exclude_place_id)
      : competitors;

    // Take only the requested number
    const selectedCompetitors = filteredCompetitors.slice(0, limit);

    console.log(`Found ${selectedCompetitors.length} competitor businesses`);

    const results = {
      total_competitors: selectedCompetitors.length,
      businesses_added: 0,
      reviews_fetched: 0,
      errors: [] as string[],
      competitors: [] as any[],
    };

    // Step 2: Process each competitor
    for (const competitor of selectedCompetitors) {
      try {
        console.log(`Processing competitor: ${competitor.title}`);

        // Use the placeId directly from the competitor data
        const placeId = competitor.placeId;
        if (!placeId) {
          results.errors.push(`No place ID found for ${competitor.title}`);
          continue;
        }

        // Check if business already exists
        const existingBusiness = await databaseService.getBusinessByPlaceId(placeId);
        let businessId: string;

        if (existingBusiness) {
          businessId = existingBusiness.id;
          console.log(`Business ${competitor.title} already exists`);
        } else {
          // Create new business
          const newBusiness = await databaseService.createBusiness({
            google_place_id: placeId,
            business_name: competitor.title,
            business_address: [
              competitor.street,
              competitor.city,
              competitor.state,
              competitor.countryCode
            ].filter(Boolean).join(', '),
            phone_number: competitor.phone || null,
            website: competitor.website || null,
          });

          businessId = newBusiness.id;
          results.businesses_added++;
          console.log(`Added new business: ${competitor.title}`);
        }

        // Step 3: Fetch reviews for this competitor (limit to 10)
        try {
          const reviewResult = await reviewService.fetchReviews(
            placeId,
            user,
            competitor.title,
            0 // No rate limiting for onboarding
          );

          results.reviews_fetched += reviewResult.new_reviews;
          console.log(`Fetched ${reviewResult.new_reviews} reviews for ${competitor.title}`);

          // Add to results
          results.competitors.push({
            business_id: businessId,
            place_id: placeId,
            name: competitor.title,
            address: [competitor.street, competitor.city, competitor.state].filter(Boolean).join(', '),
            rating: competitor.totalScore || 0,
            reviews_count: competitor.reviewsCount || 0,
            category: competitor.categories?.[0] || business_category,
            new_reviews_fetched: reviewResult.new_reviews,
          });

        } catch (reviewError) {
          console.error(`Error fetching reviews for ${competitor.title}:`, reviewError);
          results.errors.push(`Failed to fetch reviews for ${competitor.title}`);
          
          // Still add the business info even if reviews failed
          results.competitors.push({
            business_id: businessId,
            place_id: placeId,
            name: competitor.title,
            address: [competitor.street, competitor.city, competitor.state].filter(Boolean).join(', '),
            rating: competitor.totalScore || 0,
            reviews_count: competitor.reviewsCount || 0,
            category: competitor.categories?.[0] || business_category,
            new_reviews_fetched: 0,
          });
        }

      } catch (error) {
        console.error(`Error processing competitor ${competitor.title}:`, error);
        results.errors.push(`Failed to process ${competitor.title}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    console.log(`Competitor setup completed:`, {
      total: results.total_competitors,
      businesses_added: results.businesses_added,
      reviews_fetched: results.reviews_fetched,
      errors: results.errors.length,
    });

    return NextResponse.json({
      message: 'Competitor analysis setup completed',
      ...results,
    });

  } catch (error) {
    console.error('Error setting up competitors:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
