import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';

const setupCompetitorsSchema = z.object({
  business_location: z.string().min(1, 'Business location is required'),
  business_category: z.string().optional(),
  exclude_place_id: z.string().optional(),
  limit: z.number().min(1).max(20).default(15),
});

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    await requireAuth(request);

    // Parse and validate request body
    const body = await request.json();
    const { business_location, business_category } = setupCompetitorsSchema.parse(body);

    console.log(`Competitor setup request for location: ${business_location}, category: ${business_category}`);

    // Note: Competitor search functionality has been simplified
    // The old searchBusinessesByLocation method has been removed
    // You would need to implement individual business searches using scrapeBusiness

    return NextResponse.json(
      {
        error: 'Competitor setup functionality is currently unavailable. Please use individual business search instead.',
        message: 'The bulk competitor search has been replaced with simplified individual business search. Use the scrapeBusiness method for each competitor individually.'
      },
      { status: 501 }
    );

  } catch (error) {
    console.error('Error setting up competitors:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
