import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { DatabaseService } from '@/lib/services/database';

const listCompetitorsSchema = z.object({
  business_id: z.string().optional(),
  google_place_id: z.string().optional(),
  location: z.string().optional(),
  category: z.string().optional(),
  limit: z.number().min(1).max(50).default(15),
});

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireAuth(request);

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const params = {
      business_id: searchParams.get('business_id') || undefined,
      google_place_id: searchParams.get('google_place_id') || undefined,
      location: searchParams.get('location') || undefined,
      category: searchParams.get('category') || undefined,
      limit: parseInt(searchParams.get('limit') || '15'),
    };

    const { business_id, google_place_id, location, category, limit } = listCompetitorsSchema.parse(params);

    // Initialize database service
    const databaseService = new DatabaseService(true);

    let targetBusiness = null;
    let targetLocation = location;
    let targetCategory = category;

    // If business_id is provided, get the business details
    if (business_id) {
      targetBusiness = await databaseService.getBusinessById(business_id);
      if (!targetBusiness) {
        return NextResponse.json(
          { error: 'Business not found' },
          { status: 404 }
        );
      }

      // Extract location and category from business address if not provided
      if (!targetLocation && targetBusiness.business_address) {
        // Extract city and state from address
        const addressParts = targetBusiness.business_address.split(', ');
        if (addressParts.length >= 2) {
          targetLocation = addressParts.slice(-2).join(', '); // Last two parts (city, state)
        }
      }
    }

    // If google_place_id is provided, get the business details
    if (google_place_id && !targetBusiness) {
      targetBusiness = await databaseService.getBusinessByPlaceId(google_place_id);
    }

    console.log(`Finding competitors for location: ${targetLocation}, category: ${targetCategory}`);

    // Get competitor businesses from the same location
    const competitors = await databaseService.getCompetitorBusinesses({
      location: targetLocation,
      category: targetCategory,
      exclude_place_id: targetBusiness?.google_place_id,
      limit,
    });

    // For each competitor, get their recent reviews (last 10)
    const competitorsWithReviews = await Promise.all(
      competitors.map(async (competitor) => {
        try {
          const recentReviews = await databaseService.getRecentReviews(
            competitor.google_place_id,
            10
          );

          return {
            id: competitor.id,
            place_id: competitor.google_place_id,
            name: competitor.business_name,
            address: competitor.business_address,
            rating: 0, // Will be calculated from reviews
            reviews_count: recentReviews.length,
            category: targetCategory || 'Business',
            recent_reviews: recentReviews.map(review => ({
              id: review.id,
              review_text: review.review_text,
              rating: review.rating || 0,
              author_name: review.author_name || 'Anonymous',
              review_date: review.review_date,
              sentiment: review.sentiment || 'neutral',
            })),
          };
        } catch (error) {
          console.error(`Error getting reviews for competitor ${competitor.business_name}:`, error);
          return {
            id: competitor.id,
            place_id: competitor.google_place_id,
            name: competitor.business_name,
            address: competitor.business_address,
            rating: 0,
            reviews_count: 0,
            category: targetCategory || 'Business',
            recent_reviews: [],
          };
        }
      })
    );

    // Calculate average ratings for each competitor
    const competitorsWithRatings = competitorsWithReviews.map(competitor => {
      const validRatings = competitor.recent_reviews
        .map(r => r.rating)
        .filter(rating => rating > 0);
      
      const averageRating = validRatings.length > 0
        ? validRatings.reduce((sum, rating) => sum + rating, 0) / validRatings.length
        : 0;

      return {
        ...competitor,
        rating: averageRating,
      };
    });

    // Sort by rating (highest first)
    const sortedCompetitors = competitorsWithRatings.sort((a, b) => b.rating - a.rating);

    return NextResponse.json({
      message: 'Competitors retrieved successfully',
      target_business: targetBusiness ? {
        id: targetBusiness.id,
        name: targetBusiness.business_name,
        place_id: targetBusiness.google_place_id,
        address: targetBusiness.business_address,
      } : null,
      search_criteria: {
        location: targetLocation,
        category: targetCategory,
        limit,
      },
      total_competitors: sortedCompetitors.length,
      competitors: sortedCompetitors,
    });

  } catch (error) {
    console.error('Error listing competitors:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request parameters', details: error.issues },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
